<?php
/**
 * 简历文件上传处理类
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */

class ResumeUploadHandler
{
    private $config;
    private $uploadDir;
    private $allowedTypes;
    private $maxFileSize;
    
    public function __construct()
    {
        $this->initConfig();
        $this->createUploadDir();
    }
    
    /**
     * 初始化配置
     */
    private function initConfig()
    {
        $this->config = [
            'max_file_size' => 5 * 1024 * 1024, // 5MB
            'allowed_extensions' => ['pdf', 'doc', 'docx'],
            'allowed_mime_types' => [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            ],
            'upload_path' => 'xinhu/upload/resume/',
            'url_prefix' => '/xinhu/upload/resume/'
        ];
        
        $this->maxFileSize = $this->config['max_file_size'];
        $this->allowedTypes = $this->config['allowed_extensions'];
    }
    
    /**
     * 创建上传目录
     */
    private function createUploadDir()
    {
        $this->uploadDir = $this->config['upload_path'] . date('Y-m') . '/';
        
        if (!is_dir($this->uploadDir)) {
            if (!mkdir($this->uploadDir, 0755, true)) {
                throw new Exception('无法创建上传目录');
            }
        }
        
        // 创建.htaccess文件保护目录
        $htaccessFile = $this->uploadDir . '.htaccess';
        if (!file_exists($htaccessFile)) {
            $htaccessContent = "Options -Indexes\n";
            $htaccessContent .= "Order allow,deny\n";
            $htaccessContent .= "Allow from all\n";
            $htaccessContent .= "<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n";
            $htaccessContent .= "    deny from all\n";
            $htaccessContent .= "</Files>\n";
            
            file_put_contents($htaccessFile, $htaccessContent);
        }
    }
    
    /**
     * 处理文件上传
     * 
     * @param array $file $_FILES中的文件信息
     * @return array 上传结果
     */
    public function handleUpload($file)
    {
        try {
            // 基本验证
            $this->validateFile($file);
            
            // 安全检查
            $this->securityCheck($file);
            
            // 生成文件名
            $fileName = $this->generateFileName($file);
            $filePath = $this->uploadDir . $fileName;
            
            // 移动文件
            if (!move_uploaded_file($file['tmp_name'], $filePath)) {
                throw new Exception('文件保存失败');
            }
            
            // 验证文件完整性
            $this->validateUploadedFile($filePath, $file);
            
            return [
                'success' => true,
                'file_path' => $filePath,
                'file_url' => $this->config['url_prefix'] . date('Y-m') . '/' . $fileName,
                'file_name' => $fileName,
                'original_name' => $file['name'],
                'file_size' => $file['size'],
                'mime_type' => $file['type']
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * 验证文件
     * 
     * @param array $file 文件信息
     * @throws Exception
     */
    private function validateFile($file)
    {
        // 检查上传错误
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $errorMessages = [
                UPLOAD_ERR_INI_SIZE => '文件大小超过系统限制',
                UPLOAD_ERR_FORM_SIZE => '文件大小超过表单限制',
                UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
                UPLOAD_ERR_NO_FILE => '没有文件被上传',
                UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
                UPLOAD_ERR_CANT_WRITE => '文件写入失败',
                UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
            ];
            
            $message = isset($errorMessages[$file['error']]) 
                ? $errorMessages[$file['error']] 
                : '未知上传错误';
                
            throw new Exception($message);
        }
        
        // 检查文件大小
        if ($file['size'] > $this->maxFileSize) {
            throw new Exception('文件大小不能超过' . ($this->maxFileSize / 1024 / 1024) . 'MB');
        }
        
        if ($file['size'] <= 0) {
            throw new Exception('文件大小无效');
        }
        
        // 检查文件扩展名
        $pathInfo = pathinfo($file['name']);
        $extension = strtolower($pathInfo['extension'] ?? '');
        
        if (!in_array($extension, $this->allowedTypes)) {
            throw new Exception('不支持的文件类型，只支持：' . implode(', ', $this->allowedTypes));
        }
        
        // 检查MIME类型
        if (!in_array($file['type'], $this->config['allowed_mime_types'])) {
            throw new Exception('文件类型验证失败');
        }
    }
    
    /**
     * 安全检查
     * 
     * @param array $file 文件信息
     * @throws Exception
     */
    private function securityCheck($file)
    {
        // 检查文件名
        $fileName = $file['name'];
        
        // 禁止的字符
        $forbiddenChars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/'];
        foreach ($forbiddenChars as $char) {
            if (strpos($fileName, $char) !== false) {
                throw new Exception('文件名包含非法字符');
            }
        }
        
        // 检查文件名长度
        if (strlen($fileName) > 255) {
            throw new Exception('文件名过长');
        }
        
        // 检查是否为可执行文件
        $dangerousExtensions = ['php', 'phtml', 'php3', 'php4', 'php5', 'pl', 'py', 'jsp', 'asp', 'sh', 'cgi', 'exe', 'bat', 'com', 'scr'];
        $pathInfo = pathinfo($fileName);
        $extension = strtolower($pathInfo['extension'] ?? '');
        
        if (in_array($extension, $dangerousExtensions)) {
            throw new Exception('禁止上传可执行文件');
        }
        
        // 检查文件内容（简单的魔数检查）
        if (is_uploaded_file($file['tmp_name'])) {
            $fileContent = file_get_contents($file['tmp_name'], false, null, 0, 1024);
            
            // 检查是否包含PHP代码
            if (strpos($fileContent, '<?php') !== false || strpos($fileContent, '<?=') !== false) {
                throw new Exception('文件内容包含可执行代码');
            }
            
            // 检查文件头
            $this->validateFileHeader($file['tmp_name'], $extension);
        }
    }
    
    /**
     * 验证文件头
     * 
     * @param string $filePath 文件路径
     * @param string $extension 文件扩展名
     * @throws Exception
     */
    private function validateFileHeader($filePath, $extension)
    {
        $handle = fopen($filePath, 'rb');
        if (!$handle) {
            throw new Exception('无法读取文件');
        }
        
        $header = fread($handle, 8);
        fclose($handle);
        
        $validHeaders = [
            'pdf' => ['%PDF'],
            'doc' => ["\xD0\xCF\x11\xE0\xA1\xB1\x1A\xE1"], // OLE文档
            'docx' => ['PK'] // ZIP格式
        ];
        
        if (isset($validHeaders[$extension])) {
            $isValid = false;
            foreach ($validHeaders[$extension] as $validHeader) {
                if (strpos($header, $validHeader) === 0) {
                    $isValid = true;
                    break;
                }
            }
            
            if (!$isValid) {
                throw new Exception('文件格式验证失败');
            }
        }
    }
    
    /**
     * 生成安全的文件名
     * 
     * @param array $file 文件信息
     * @return string
     */
    private function generateFileName($file)
    {
        $pathInfo = pathinfo($file['name']);
        $extension = strtolower($pathInfo['extension'] ?? '');
        
        // 生成唯一文件名
        $timestamp = date('YmdHis');
        $randomString = substr(md5(uniqid(mt_rand(), true)), 0, 8);
        
        return $timestamp . '_' . $randomString . '.' . $extension;
    }
    
    /**
     * 验证上传后的文件
     * 
     * @param string $filePath 文件路径
     * @param array $originalFile 原始文件信息
     * @throws Exception
     */
    private function validateUploadedFile($filePath, $originalFile)
    {
        // 检查文件是否存在
        if (!file_exists($filePath)) {
            throw new Exception('文件保存失败');
        }
        
        // 检查文件大小
        $actualSize = filesize($filePath);
        if ($actualSize !== $originalFile['size']) {
            unlink($filePath); // 删除损坏的文件
            throw new Exception('文件大小验证失败');
        }
        
        // 设置文件权限
        chmod($filePath, 0644);
    }
    
    /**
     * 删除文件
     * 
     * @param string $filePath 文件路径
     * @return bool
     */
    public function deleteFile($filePath)
    {
        if (file_exists($filePath) && is_file($filePath)) {
            return unlink($filePath);
        }
        return true;
    }
    
    /**
     * 获取文件信息
     * 
     * @param string $filePath 文件路径
     * @return array|false
     */
    public function getFileInfo($filePath)
    {
        if (!file_exists($filePath)) {
            return false;
        }
        
        $pathInfo = pathinfo($filePath);
        
        return [
            'file_path' => $filePath,
            'file_name' => $pathInfo['basename'],
            'file_size' => filesize($filePath),
            'file_extension' => strtolower($pathInfo['extension'] ?? ''),
            'mime_type' => mime_content_type($filePath),
            'created_time' => filectime($filePath),
            'modified_time' => filemtime($filePath)
        ];
    }
    
    /**
     * 清理过期文件
     * 
     * @param int $days 保留天数
     * @return int 清理的文件数量
     */
    public function cleanupOldFiles($days = 30)
    {
        $cleanupTime = time() - ($days * 24 * 60 * 60);
        $cleanedCount = 0;
        
        $baseDir = $this->config['upload_path'];
        if (!is_dir($baseDir)) {
            return 0;
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($baseDir),
            RecursiveIteratorIterator::LEAVES_ONLY
        );
        
        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getMTime() < $cleanupTime) {
                if (unlink($file->getPathname())) {
                    $cleanedCount++;
                }
            }
        }
        
        return $cleanedCount;
    }
    
    /**
     * 获取上传配置信息
     * 
     * @return array
     */
    public function getUploadConfig()
    {
        return [
            'max_file_size' => $this->maxFileSize,
            'max_file_size_mb' => round($this->maxFileSize / 1024 / 1024, 1),
            'allowed_types' => $this->allowedTypes,
            'allowed_mime_types' => $this->config['allowed_mime_types']
        ];
    }
}
