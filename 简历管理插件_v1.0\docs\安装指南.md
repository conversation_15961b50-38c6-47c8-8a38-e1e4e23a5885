# 简历管理插件安装指南

## 📋 安装前准备

### 系统要求
- 信呼OA系统 2.5.0 或更高版本
- PHP 7.0 或更高版本
- MySQL 5.6 或更高版本
- 支持文件上传功能

### 环境检查
在安装前，请确保以下条件满足：

1. **PHP扩展检查**
   ```bash
   php -m | grep -E "(pdo|pdo_mysql|gd|fileinfo|json)"
   ```

2. **目录权限检查**
   ```bash
   # 确保以下目录可写
   chmod 755 webmain/main
   chmod 755 web
   chmod 755 upload
   ```

3. **内存限制检查**
   ```php
   // PHP内存限制建议至少128M
   ini_get('memory_limit');
   ```

## 🚀 安装方法

### 方法一：插件管理器安装（推荐）

1. **上传插件包**
   - 将 `简历管理插件_v1.0.zip` 上传到服务器
   - 解压到 `xinhu/plugins/` 目录

2. **后台安装**
   - 登录信呼系统后台
   - 访问：系统管理 → 插件管理
   - 找到"简历管理插件"
   - 点击"安装"按钮

3. **验证安装**
   - 刷新页面
   - 在"人事档案"菜单下查看"简历管理"

### 方法二：直接执行安装脚本

1. **上传文件**
   ```bash
   # 上传到插件目录
   unzip 简历管理插件_v1.0.zip -d xinhu/plugins/
   cd xinhu/plugins/简历管理插件_v1.0/
   ```

2. **执行安装**
   ```bash
   # 访问安装脚本
   curl http://your-domain.com/xinhu/plugins/简历管理插件_v1.0/安装脚本.php
   ```

3. **检查结果**
   - 查看返回的JSON结果
   - 确认 `"success": true`

### 方法三：手动安装（兼容模式）

1. **复制文件**
   ```bash
   # 复制后端文件
   cp files/webmain/model/resumeModel.php xinhu/webmain/model/
   cp -r files/webmain/main/resume xinhu/webmain/main/
   cp -r files/webmain/include xinhu/webmain/
   
   # 复制前端文件
   cp -r files/web/resume xinhu/web/
   
   # 创建上传目录
   mkdir -p xinhu/upload/resume
   cp files/upload/resume/.htaccess xinhu/upload/resume/
   ```

2. **执行SQL脚本**
   - 在数据库管理工具中执行 `sql/安装脚本.sql`
   - 或通过"系统管理 → 系统升级"执行

3. **配置权限**
   - 在"系统管理 → 权限管理"中分配权限

## ⚙️ 安装后配置

### 1. 权限分配

访问：系统管理 → 权限管理

为相关角色分配以下权限：
- `resume_view`: 查看简历
- `resume_edit`: 编辑简历
- `resume_delete`: 删除简历
- `resume_audit`: 审核简历
- `resume_export`: 导出简历
- `resume_stats`: 查看统计

### 2. 系统设置

访问：系统管理 → 系统设置

配置以下选项：
- 简历文件大小限制：默认10MB
- 简历文件类型限制：pdf,doc,docx,jpg,jpeg,png
- 简历外链访问限制：启用IP频率限制
- 简历访问频率限制：每小时10次

### 3. 外链配置

外链访问地址：`http://your-domain.com/xinhu/web/resume/`

可以通过以下方式分享：
- 直接提供链接
- 生成二维码
- 嵌入到招聘页面

## 🔧 高级配置

### 1. 自定义上传路径

编辑配置文件 `config/默认设置.json`：
```json
{
    "upload": {
        "upload_path": "upload/custom_resume/"
    }
}
```

### 2. 邮件通知配置

```json
{
    "notification": {
        "email_admin": true,
        "admin_emails": ["<EMAIL>"],
        "hr_emails": ["<EMAIL>"]
    }
}
```

### 3. 安全设置

```json
{
    "security": {
        "rate_limit": 5,
        "rate_window": 3600,
        "enable_captcha": true,
        "allowed_ips": ["***********/24"]
    }
}
```

## 🧪 安装验证

### 1. 功能测试

- [ ] 后台菜单显示正常
- [ ] 简历列表页面可访问
- [ ] 外链表单可正常提交
- [ ] 文件上传功能正常
- [ ] 审核功能正常
- [ ] 统计页面显示正常

### 2. 权限测试

- [ ] 不同角色权限生效
- [ ] 无权限用户被正确拦截
- [ ] 菜单根据权限显示/隐藏

### 3. 安全测试

- [ ] IP频率限制生效
- [ ] 文件类型限制生效
- [ ] 恶意文件被拦截

## ❌ 故障排除

### 常见问题

1. **安装失败：数据库连接错误**
   - 检查数据库配置
   - 确认数据库用户权限

2. **菜单不显示**
   - 清除系统缓存
   - 检查权限分配
   - 重新登录系统

3. **文件上传失败**
   - 检查upload目录权限
   - 确认PHP上传限制
   - 检查磁盘空间

4. **外链页面404**
   - 检查web目录权限
   - 确认URL重写规则
   - 检查.htaccess文件

### 日志查看

- **安装日志**: 插件安装过程的详细日志
- **错误日志**: 系统错误和异常记录
- **访问日志**: 外链页面的访问记录

### 获取帮助

如果遇到问题：
1. 查看安装日志
2. 检查系统错误日志
3. 联系技术支持：<EMAIL>

## 📞 技术支持

- **邮箱**: <EMAIL>
- **网站**: https://www.rockoa.com
- **文档**: https://docs.rockoa.com

---

**安装指南版本**: 1.0.0  
**更新日期**: 2025-06-21
