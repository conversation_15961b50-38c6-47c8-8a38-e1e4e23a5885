# 简历管理插件打包指南

## 📦 最终插件包结构

### 完整目录结构
```
xinhu_resume_plugin_v1.0/
├── plugin.json                          # ✅ 插件配置文件
├── install.php                          # ✅ 安装脚本
├── uninstall.php                        # ✅ 卸载脚本
├── README.md                            # ✅ 插件说明文档
├── CHANGELOG.md                         # 📝 更新日志
├── LICENSE                              # 📝 许可证文件
├── 
├── sql/                                 # 数据库脚本目录
│   ├── install.sql                      # ✅ 安装SQL脚本
│   ├── upgrade.sql                      # 📝 升级SQL脚本
│   └── uninstall.sql                    # 📝 卸载SQL脚本
├── 
├── files/                               # 插件文件目录
│   ├── webmain/                         # 后端文件
│   │   ├── model/
│   │   │   └── resumeModel.php          # ✅ 数据模型
│   │   ├── main/resume/                 # 控制器和视图
│   │   │   ├── resumeAction.php         # ✅ 控制器
│   │   │   ├── tpl_resume.html          # ✅ 列表页面
│   │   │   ├── tpl_resume_detail.html   # ✅ 详情页面
│   │   │   └── rock_resume.php          # 📝 页面脚本
│   │   └── include/                     # 工具类
│   │       ├── ResumeUploadHandler.php  # ✅ 文件上传处理
│   │       └── ResumeSecurityManager.php # ✅ 安全管理
│   ├── 
│   ├── web/resume/                      # 外链访问文件
│   │   ├── index.html                   # ✅ 简历填写页面
│   │   ├── submit.php                   # ✅ 提交处理
│   │   ├── assets/                      # 静态资源
│   │   │   ├── css/
│   │   │   │   └── resume.css           # 📝 样式文件
│   │   │   ├── js/
│   │   │   │   └── resume.js            # 📝 脚本文件
│   │   │   └── images/                  # 📝 图片资源
│   │   └── .htaccess                    # 📝 访问控制
│   └── 
│   └── upload/resume/                   # 上传目录
│       ├── .htaccess                    # 📝 安全配置
│       └── index.html                   # 📝 防止目录浏览
├── 
├── config/                              # 配置文件目录
│   ├── menu.json                        # 📝 菜单配置
│   ├── permission.json                  # 📝 权限配置
│   └── settings.json                    # 📝 默认设置
├── 
├── docs/                                # 文档目录
│   ├── installation.md                  # 📝 安装说明
│   ├── user_guide.md                    # 📝 使用指南
│   ├── api_reference.md                 # 📝 API参考
│   └── troubleshooting.md               # 📝 故障排除
├── 
├── tests/                               # 测试文件目录
│   ├── unit/                            # 📝 单元测试
│   ├── integration/                     # 📝 集成测试
│   └── fixtures/                        # 📝 测试数据
└── 
└── screenshots/                         # 截图目录
    ├── admin_list.png                   # 📝 管理界面截图
    ├── admin_detail.png                 # 📝 详情页截图
    └── public_form.png                  # 📝 外链表单截图
```

**图例说明：**
- ✅ 已完成的文件
- 📝 需要补充的文件

## 🔧 文件映射关系

### 当前文件 → 插件包位置

```bash
# 核心配置文件
plugin.json → xinhu_resume_plugin_v1.0/plugin.json
install.php → xinhu_resume_plugin_v1.0/install.php
uninstall.php → xinhu_resume_plugin_v1.0/uninstall.php

# 数据库脚本
resume_upgrade.sql → xinhu_resume_plugin_v1.0/sql/install.sql

# 后端文件
resumeModel.php → xinhu_resume_plugin_v1.0/files/webmain/model/resumeModel.php
resumeAction.php → xinhu_resume_plugin_v1.0/files/webmain/main/resume/resumeAction.php
tpl_resume.html → xinhu_resume_plugin_v1.0/files/webmain/main/resume/tpl_resume.html
tpl_resume_detail.html → xinhu_resume_plugin_v1.0/files/webmain/main/resume/tpl_resume_detail.html

# 工具类文件
resume_upload_handler.php → xinhu_resume_plugin_v1.0/files/webmain/include/ResumeUploadHandler.php
resume_security.php → xinhu_resume_plugin_v1.0/files/webmain/include/ResumeSecurityManager.php

# 外链文件
resume_public_form.html → xinhu_resume_plugin_v1.0/files/web/resume/index.html
resume_submit.php → xinhu_resume_plugin_v1.0/files/web/resume/submit.php

# 文档文件
README_RESUME_MODULE.md → xinhu_resume_plugin_v1.0/README.md
DEPLOYMENT_CHECKLIST.md → xinhu_resume_plugin_v1.0/docs/installation.md
```

## 📋 打包步骤

### 1. 创建插件目录结构
```bash
mkdir -p xinhu_resume_plugin_v1.0/{sql,files/{webmain/{model,main/resume,include},web/resume,upload/resume},config,docs,tests,screenshots}
```

### 2. 复制核心文件
```bash
# 配置和安装文件
cp plugin.json xinhu_resume_plugin_v1.0/
cp install.php xinhu_resume_plugin_v1.0/
cp uninstall.php xinhu_resume_plugin_v1.0/

# 数据库脚本
cp resume_upgrade.sql xinhu_resume_plugin_v1.0/sql/install.sql

# 后端文件
cp resumeModel.php xinhu_resume_plugin_v1.0/files/webmain/model/
cp resumeAction.php xinhu_resume_plugin_v1.0/files/webmain/main/resume/
cp tpl_resume.html xinhu_resume_plugin_v1.0/files/webmain/main/resume/
cp tpl_resume_detail.html xinhu_resume_plugin_v1.0/files/webmain/main/resume/

# 工具类
cp resume_upload_handler.php xinhu_resume_plugin_v1.0/files/webmain/include/ResumeUploadHandler.php
cp resume_security.php xinhu_resume_plugin_v1.0/files/webmain/include/ResumeSecurityManager.php

# 外链文件
cp resume_public_form.html xinhu_resume_plugin_v1.0/files/web/resume/index.html
cp resume_submit.php xinhu_resume_plugin_v1.0/files/web/resume/submit.php

# 文档
cp README_RESUME_MODULE.md xinhu_resume_plugin_v1.0/README.md
```

### 3. 创建必要的保护文件
```bash
# 上传目录保护
cat > xinhu_resume_plugin_v1.0/files/upload/resume/.htaccess << 'EOF'
Options -Indexes
Order allow,deny
Allow from all
<Files ~ "\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$">
    deny from all
</Files>
EOF

cat > xinhu_resume_plugin_v1.0/files/upload/resume/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head><title>403 Forbidden</title></head>
<body><h1>Directory access is forbidden.</h1></body>
</html>
EOF

# Web目录保护
cat > xinhu_resume_plugin_v1.0/files/web/resume/.htaccess << 'EOF'
<Files ~ "\.(php)$">
    Order allow,deny
    Allow from all
</Files>
EOF
```

### 4. 创建许可证文件
```bash
cat > xinhu_resume_plugin_v1.0/LICENSE << 'EOF'
MIT License

Copyright (c) 2025 信呼开发团队

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
EOF
```

### 5. 创建更新日志
```bash
cat > xinhu_resume_plugin_v1.0/CHANGELOG.md << 'EOF'
# 更新日志

## [1.0.0] - 2025-06-21

### 新增功能
- 完整的简历管理功能
- 外链简历填写页面
- 后台管理和审核功能
- 文件上传和安全控制
- 数据统计和导出功能
- 简历转员工档案功能
- 完善的权限控制系统

### 技术特性
- 标准化插件结构
- 自动安装和卸载
- 安全防护机制
- 响应式设计
- API接口支持

### 文档
- 完整的安装指南
- 详细的使用说明
- API参考文档
- 故障排除指南
EOF
```

### 6. 设置文件权限
```bash
# 设置目录权限
find xinhu_resume_plugin_v1.0 -type d -exec chmod 755 {} \;

# 设置文件权限
find xinhu_resume_plugin_v1.0 -type f -exec chmod 644 {} \;

# 设置可执行权限
chmod 755 xinhu_resume_plugin_v1.0/install.php
chmod 755 xinhu_resume_plugin_v1.0/uninstall.php
```

### 7. 创建压缩包
```bash
# 创建ZIP压缩包
zip -r xinhu_resume_plugin_v1.0.zip xinhu_resume_plugin_v1.0/

# 或创建TAR.GZ压缩包
tar -czf xinhu_resume_plugin_v1.0.tar.gz xinhu_resume_plugin_v1.0/
```

## 🚀 安装部署

### 插件安装位置
```
xinhu/
├── plugins/                     # 插件根目录
│   └── resume_manager/          # 简历管理插件
│       ├── plugin.json
│       ├── install.php
│       └── ...
├── webmain/                     # 系统核心（插件文件会复制到这里）
├── web/                         # 前端访问（外链文件会复制到这里）
└── upload/                      # 上传目录（上传文件会保存到这里）
```

### 安装命令
```bash
# 1. 上传并解压插件包
cd /path/to/xinhu/plugins/
unzip xinhu_resume_plugin_v1.0.zip

# 2. 重命名目录（可选）
mv xinhu_resume_plugin_v1.0 resume_manager

# 3. 执行安装
curl http://your-domain.com/xinhu/plugins/resume_manager/install.php

# 4. 或通过后台安装
# 访问：系统管理 → 插件管理 → 安装插件
```

## ✅ 验证清单

### 安装前检查
- [ ] 插件包结构完整
- [ ] 所有必需文件存在
- [ ] 配置文件格式正确
- [ ] 文件权限设置正确

### 安装后验证
- [ ] 数据表创建成功
- [ ] 菜单显示正常
- [ ] 文件复制到位
- [ ] 外链页面可访问
- [ ] 后台功能正常
- [ ] 权限配置生效

### 功能测试
- [ ] 简历提交功能
- [ ] 文件上传功能
- [ ] 后台管理功能
- [ ] 审核流程功能
- [ ] 数据统计功能
- [ ] 导出功能

## 📞 技术支持

如果在打包或安装过程中遇到问题：

1. 检查文件结构是否完整
2. 验证配置文件格式
3. 查看安装日志
4. 检查系统权限
5. 联系技术支持

---

**插件版本：** 1.0.0  
**打包日期：** 2025-06-21  
**兼容版本：** 信呼OA 2.5.0+
