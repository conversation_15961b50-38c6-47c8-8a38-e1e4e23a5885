<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线简历提交 - 人才招聘</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        .resume-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            margin: 30px auto;
            max-width: 800px;
            overflow: hidden;
        }
        .resume-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .resume-header h1 {
            margin: 0;
            font-size: 2.5rem;
            font-weight: 300;
        }
        .resume-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .resume-body {
            padding: 40px;
        }
        .form-section {
            margin-bottom: 30px;
        }
        .section-title {
            color: #667eea;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #f0f0f0;
        }
        .form-group label {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .file-upload-area {
            border: 2px dashed #667eea;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            background: #f8f9ff;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .file-upload-area:hover {
            background: #f0f2ff;
            border-color: #5a67d8;
        }
        .file-upload-area i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }
        .btn-submit {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 25px;
            color: white;
            font-size: 1.1rem;
            font-weight: 600;
            padding: 15px 40px;
            transition: all 0.3s ease;
            width: 100%;
        }
        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .required {
            color: #e74c3c;
        }
        .progress {
            height: 6px;
            border-radius: 3px;
            margin-top: 10px;
            display: none;
        }
        .alert {
            border-radius: 8px;
            border: none;
        }
        @media (max-width: 768px) {
            .resume-container {
                margin: 15px;
            }
            .resume-body {
                padding: 20px;
            }
            .resume-header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="resume-container">
            <!-- 头部 -->
            <div class="resume-header">
                <h1><i class="fas fa-file-alt"></i> 在线简历提交</h1>
                <p>请填写完整信息，我们会尽快与您联系</p>
            </div>

            <!-- 表单内容 -->
            <div class="resume-body">
                <form id="resumeForm" enctype="multipart/form-data">
                    <!-- 基本信息 -->
                    <div class="form-section">
                        <h3 class="section-title"><i class="fas fa-user"></i> 基本信息</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="name">姓名 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="gender">性别</label>
                                    <select class="form-control" id="gender" name="gender">
                                        <option value="">请选择</option>
                                        <option value="1">男</option>
                                        <option value="2">女</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="mobile">手机号 <span class="required">*</span></label>
                                    <input type="tel" class="form-control" id="mobile" name="mobile" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="email">邮箱</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="birth_date">出生日期</label>
                                    <input type="date" class="form-control" id="birth_date" name="birth_date">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="education">学历</label>
                                    <select class="form-control" id="education" name="education">
                                        <option value="">请选择</option>
                                        <option value="高中">高中</option>
                                        <option value="大专">大专</option>
                                        <option value="本科">本科</option>
                                        <option value="硕士">硕士</option>
                                        <option value="博士">博士</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 求职信息 -->
                    <div class="form-section">
                        <h3 class="section-title"><i class="fas fa-briefcase"></i> 求职信息</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="position">应聘职位 <span class="required">*</span></label>
                                    <input type="text" class="form-control" id="position" name="position" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="experience_years">工作年限</label>
                                    <select class="form-control" id="experience_years" name="experience_years">
                                        <option value="">请选择</option>
                                        <option value="0">应届毕业生</option>
                                        <option value="1">1年</option>
                                        <option value="2">2年</option>
                                        <option value="3">3年</option>
                                        <option value="4">4年</option>
                                        <option value="5">5年以上</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="current_salary">当前薪资</label>
                                    <input type="number" class="form-control" id="current_salary" name="current_salary" placeholder="单位：元">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="expected_salary">期望薪资</label>
                                    <input type="number" class="form-control" id="expected_salary" name="expected_salary" placeholder="单位：元">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="available_date">可到岗日期</label>
                            <input type="date" class="form-control" id="available_date" name="available_date">
                        </div>
                    </div>

                    <!-- 详细信息 -->
                    <div class="form-section">
                        <h3 class="section-title"><i class="fas fa-info-circle"></i> 详细信息</h3>
                        <div class="form-group">
                            <label for="work_experience">工作经历</label>
                            <textarea class="form-control" id="work_experience" name="work_experience" rows="4" placeholder="请详细描述您的工作经历..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="education_background">教育背景</label>
                            <textarea class="form-control" id="education_background" name="education_background" rows="3" placeholder="请描述您的教育背景..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="skills">技能特长</label>
                            <textarea class="form-control" id="skills" name="skills" rows="3" placeholder="请描述您的技能特长..."></textarea>
                        </div>
                        <div class="form-group">
                            <label for="self_introduction">自我介绍</label>
                            <textarea class="form-control" id="self_introduction" name="self_introduction" rows="4" placeholder="请简单介绍一下自己..."></textarea>
                        </div>
                    </div>

                    <!-- 简历上传 -->
                    <div class="form-section">
                        <h3 class="section-title"><i class="fas fa-upload"></i> 简历上传</h3>
                        <div class="file-upload-area" onclick="document.getElementById('resume_file').click()">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <h5>点击上传简历文件</h5>
                            <p class="text-muted">支持 PDF、DOC、DOCX 格式，文件大小不超过 5MB</p>
                            <input type="file" id="resume_file" name="resume_file" accept=".pdf,.doc,.docx" style="display: none;">
                        </div>
                        <div id="file-info" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <i class="fas fa-file"></i> <span id="file-name"></span>
                                <button type="button" class="btn btn-sm btn-outline-danger float-right" onclick="clearFile()">
                                    <i class="fas fa-times"></i> 删除
                                </button>
                            </div>
                        </div>
                        <div class="progress" id="upload-progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                    </div>

                    <!-- 联系信息 -->
                    <div class="form-section">
                        <h3 class="section-title"><i class="fas fa-phone"></i> 紧急联系人</h3>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="emergency_contact">联系人姓名</label>
                                    <input type="text" class="form-control" id="emergency_contact" name="emergency_contact">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="emergency_phone">联系人电话</label>
                                    <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone">
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="address">现居住地址</label>
                            <input type="text" class="form-control" id="address" name="address">
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-submit">
                            <i class="fas fa-paper-plane"></i> 提交简历
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 成功提示模态框 -->
    <div class="modal fade" id="successModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                    <h4 class="mt-3">提交成功！</h4>
                    <p class="text-muted">您的简历已成功提交，我们会尽快与您联系。</p>
                    <button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/4.6.0/js/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // 文件选择处理
            $('#resume_file').change(function() {
                var file = this.files[0];
                if (file) {
                    // 检查文件类型
                    var allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
                    if (allowedTypes.indexOf(file.type) === -1) {
                        alert('请选择 PDF、DOC 或 DOCX 格式的文件');
                        this.value = '';
                        return;
                    }
                    
                    // 检查文件大小 (5MB)
                    if (file.size > 5 * 1024 * 1024) {
                        alert('文件大小不能超过 5MB');
                        this.value = '';
                        return;
                    }
                    
                    $('#file-name').text(file.name);
                    $('#file-info').show();
                } else {
                    $('#file-info').hide();
                }
            });

            // 表单提交处理
            $('#resumeForm').submit(function(e) {
                e.preventDefault();
                
                // 基本验证
                if (!$('#name').val().trim()) {
                    alert('请填写姓名');
                    $('#name').focus();
                    return;
                }
                
                if (!$('#mobile').val().trim()) {
                    alert('请填写手机号');
                    $('#mobile').focus();
                    return;
                }
                
                // 手机号格式验证
                var mobilePattern = /^1[3-9]\d{9}$/;
                if (!mobilePattern.test($('#mobile').val().trim())) {
                    alert('请填写正确的手机号');
                    $('#mobile').focus();
                    return;
                }
                
                if (!$('#position').val().trim()) {
                    alert('请填写应聘职位');
                    $('#position').focus();
                    return;
                }
                
                // 禁用提交按钮
                $('.btn-submit').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 提交中...');
                
                // 创建FormData对象
                var formData = new FormData(this);
                
                // Ajax提交
                $.ajax({
                    url: 'submit.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhr: function() {
                        var xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener("progress", function(evt) {
                            if (evt.lengthComputable) {
                                var percentComplete = evt.loaded / evt.total * 100;
                                $('#upload-progress').show();
                                $('.progress-bar').css('width', percentComplete + '%');
                            }
                        }, false);
                        return xhr;
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#successModal').modal('show');
                            $('#resumeForm')[0].reset();
                            $('#file-info').hide();
                            $('#upload-progress').hide();
                        } else {
                            alert(response.message || '提交失败，请重试');
                        }
                    },
                    error: function() {
                        alert('网络错误，请重试');
                    },
                    complete: function() {
                        $('.btn-submit').prop('disabled', false).html('<i class="fas fa-paper-plane"></i> 提交简历');
                    }
                });
            });
        });

        // 清除文件
        function clearFile() {
            $('#resume_file').val('');
            $('#file-info').hide();
        }
    </script>
</body>
</html>
