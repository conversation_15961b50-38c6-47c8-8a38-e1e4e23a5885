# 简历管理插件文件整理说明

## 📁 标准插件目录结构

请按照以下结构重新整理文件：

```
简历管理插件_v1.0/
├── 插件配置.json                        # plugin.json
├── 安装脚本.php                         # install.php  
├── 卸载脚本.php                         # uninstall.php
├── 插件说明.md                          # README.md
├── 更新日志.md                          # CHANGELOG.md
├── 许可证.txt                           # LICENSE
├── 
├── sql/                                # 数据库脚本目录
│   ├── 安装脚本.sql                     # install.sql (原resume_upgrade.sql)
│   ├── 升级脚本.sql                     # upgrade.sql
│   └── 卸载脚本.sql                     # uninstall.sql
├── 
├── files/                              # 插件文件目录
│   ├── webmain/                        # 后端文件
│   │   ├── model/
│   │   │   └── resumeModel.php         # 数据模型
│   │   ├── main/resume/                # 控制器和视图
│   │   │   ├── resumeAction.php        # 控制器
│   │   │   ├── tpl_resume.html         # 列表页面
│   │   │   ├── tpl_resume_detail.html  # 详情页面
│   │   │   └── rock_resume.php         # 页面脚本
│   │   └── include/                    # 工具类
│   │       ├── ResumeUploadHandler.php # 文件上传处理
│   │       └── ResumeSecurityManager.php # 安全管理
│   ├── 
│   ├── web/resume/                     # 外链访问文件
│   │   ├── index.html                  # 简历填写页面
│   │   ├── submit.php                  # 提交处理
│   │   ├── assets/                     # 静态资源
│   │   │   ├── css/
│   │   │   │   └── resume.css          # 样式文件
│   │   │   ├── js/
│   │   │   │   └── resume.js           # 脚本文件
│   │   │   └── images/                 # 图片资源
│   │   └── .htaccess                   # 访问控制
│   └── 
│   └── upload/resume/                  # 上传目录
│       ├── .htaccess                   # 安全配置
│       └── index.html                  # 防止目录浏览
├── 
├── docs/                               # 文档目录
│   ├── 安装指南.md                      # installation.md
│   ├── 使用手册.md                      # user_guide.md
│   ├── 接口文档.md                      # api_reference.md
│   ├── 故障排除.md                      # troubleshooting.md
│   └── 部署检查清单.md                   # deployment_checklist.md
├── 
├── config/                             # 配置文件目录
│   ├── 菜单配置.json                    # menu.json
│   ├── 权限配置.json                    # permission.json
│   └── 默认设置.json                    # settings.json
├── 
├── tests/                              # 测试文件目录
│   ├── unit/                           # 单元测试
│   ├── integration/                    # 集成测试
│   └── fixtures/                       # 测试数据
└── 
└── screenshots/                        # 截图目录
    ├── 管理界面.png                     # admin_list.png
    ├── 详情页面.png                     # admin_detail.png
    ├── 外链表单.png                     # public_form.png
    └── 统计面板.png                     # stats_dashboard.png
```

## 🔄 文件重命名和移动操作

### 1. 创建主目录
```bash
mkdir "简历管理插件_v1.0"
cd "简历管理插件_v1.0"
```

### 2. 创建子目录结构
```bash
mkdir -p sql files/{webmain/{model,main/resume,include},web/resume/assets/{css,js,images},upload/resume} docs config tests/{unit,integration,fixtures} screenshots
```

### 3. 移动和重命名文件

#### 核心配置文件
```bash
# 插件配置
mv plugin.json 插件配置.json

# 安装卸载脚本
mv install.php 安装脚本.php
mv uninstall.php 卸载脚本.php

# 文档文件
mv README_RESUME_MODULE.md 插件说明.md
mv DEPLOYMENT_CHECKLIST.md docs/部署检查清单.md
mv resume_plugin_structure.md docs/插件结构说明.md
mv PLUGIN_PACKAGE_GUIDE.md docs/打包指南.md
```

#### 数据库脚本
```bash
mv resume_upgrade.sql sql/安装脚本.sql
```

#### 后端文件
```bash
# 数据模型
mv resumeModel.php files/webmain/model/

# 控制器和视图
mv resumeAction.php files/webmain/main/resume/
mv tpl_resume.html files/webmain/main/resume/
mv tpl_resume_detail.html files/webmain/main/resume/

# 工具类
mv resume_upload_handler.php files/webmain/include/ResumeUploadHandler.php
mv resume_security.php files/webmain/include/ResumeSecurityManager.php
```

#### 外链文件
```bash
mv resume_public_form.html files/web/resume/index.html
mv resume_submit.php files/web/resume/submit.php
```

#### 设计文档
```bash
mv resume_module_design.md docs/架构设计.md
```

### 4. 创建保护文件

#### 上传目录保护
```bash
cat > files/upload/resume/.htaccess << 'EOF'
Options -Indexes
Order allow,deny
Allow from all
<Files ~ "\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$">
    deny from all
</Files>
EOF

cat > files/upload/resume/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head><title>403 Forbidden</title></head>
<body><h1>Directory access is forbidden.</h1></body>
</html>
EOF
```

#### Web目录保护
```bash
cat > files/web/resume/.htaccess << 'EOF'
<Files ~ "\.(php)$">
    Order allow,deny
    Allow from all
</Files>
EOF
```

### 5. 创建许可证和更新日志

#### 许可证文件
```bash
cat > 许可证.txt << 'EOF'
MIT License

Copyright (c) 2025 信呼开发团队

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
EOF
```

#### 更新日志
```bash
cat > 更新日志.md << 'EOF'
# 更新日志

## [1.0.0] - 2025-06-21

### 新增功能
- ✅ 完整的简历管理功能
- ✅ 外链简历填写页面
- ✅ 后台管理和审核功能
- ✅ 文件上传和安全控制
- ✅ 数据统计和导出功能
- ✅ 简历转员工档案功能
- ✅ 完善的权限控制系统

### 技术特性
- 🔧 标准化插件结构
- 🔧 自动安装和卸载
- 🔧 安全防护机制
- 🔧 响应式设计
- 🔧 API接口支持

### 文档
- 📖 完整的安装指南
- 📖 详细的使用说明
- 📖 API参考文档
- 📖 故障排除指南

### 安全性
- 🛡️ IP访问频率限制
- 🛡️ 文件上传安全检查
- 🛡️ SQL注入防护
- 🛡️ XSS攻击防护
- 🛡️ CSRF令牌验证
EOF
```

## 📦 最终打包

### 1. 设置文件权限
```bash
# 设置目录权限
find "简历管理插件_v1.0" -type d -exec chmod 755 {} \;

# 设置文件权限
find "简历管理插件_v1.0" -type f -exec chmod 644 {} \;

# 设置可执行权限
chmod 755 "简历管理插件_v1.0/安装脚本.php"
chmod 755 "简历管理插件_v1.0/卸载脚本.php"
```

### 2. 创建压缩包
```bash
# 创建ZIP压缩包
zip -r "简历管理插件_v1.0.zip" "简历管理插件_v1.0/"

# 或创建TAR.GZ压缩包
tar -czf "简历管理插件_v1.0.tar.gz" "简历管理插件_v1.0/"
```

## 🎯 整理后的优势

### ✅ **文件组织清晰**
- 所有文件都在一个主目录下
- 按功能分类到不同子目录
- 中文命名便于理解

### ✅ **符合中文习惯**
- 文档使用中文命名
- 目录结构清晰明了
- 便于中文用户使用

### ✅ **便于分发**
- 单一压缩包包含所有文件
- 标准化的插件结构
- 完整的安装和使用文档

### ✅ **易于维护**
- 文件分类明确
- 版本管理方便
- 升级更新简单

## 📞 使用说明

整理完成后，用户只需要：

1. **下载插件包** - `简历管理插件_v1.0.zip`
2. **解压到插件目录** - `xinhu/plugins/`
3. **执行安装** - 通过后台或直接访问安装脚本
4. **开始使用** - 在人事档案菜单下找到简历管理

这样整理后的插件包更加专业和规范！
