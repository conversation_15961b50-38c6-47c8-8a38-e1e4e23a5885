<?php
/**
 * 简历管理数据模型
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */
class resumeClassModel extends Model
{
    /**
     * 初始化模型
     */
    public function initModel()
    {
        $this->settable('resume');
    }

    /**
     * 获取简历列表
     * 
     * @param string $where 查询条件
     * @param string $fields 查询字段
     * @param string $order 排序
     * @param int $limit 限制数量
     * @return array
     */
    public function getResumeList($where = '1=1', $fields = '*', $order = 'submit_time DESC', $limit = 0)
    {
        $sql = "SELECT {$fields} FROM `{$this->table}` WHERE {$where}";
        if (!empty($order)) {
            $sql .= " ORDER BY {$order}";
        }
        if ($limit > 0) {
            $sql .= " LIMIT {$limit}";
        }
        
        return $this->db->getall($sql);
    }

    /**
     * 获取简历详情（包含详细信息）
     * 
     * @param int $id 简历ID
     * @return array|false
     */
    public function getResumeDetail($id)
    {
        $sql = "SELECT r.*, rd.* FROM `{$this->table}` r 
                LEFT JOIN `{$this->perfix}resume_detail` rd ON r.id = rd.resume_id 
                WHERE r.id = {$id}";
        
        return $this->db->getone($sql);
    }

    /**
     * 保存简历基本信息
     * 
     * @param array $data 简历数据
     * @return int|false 返回简历ID或false
     */
    public function saveResume($data)
    {
        // 数据验证
        if (empty($data['name']) || empty($data['mobile'])) {
            return false;
        }

        // 检查手机号是否已存在
        if ($this->checkMobileExists($data['mobile'], isset($data['id']) ? $data['id'] : 0)) {
            return false;
        }

        // 设置默认值
        $data['submit_time'] = isset($data['submit_time']) ? $data['submit_time'] : $this->rock->now;
        $data['ip_address'] = isset($data['ip_address']) ? $data['ip_address'] : $this->rock->ip;
        $data['user_agent'] = isset($data['user_agent']) ? $data['user_agent'] : $_SERVER['HTTP_USER_AGENT'];

        if (isset($data['id']) && $data['id'] > 0) {
            // 更新
            $id = $data['id'];
            unset($data['id']);
            $result = $this->update($data, $id);
            return $result ? $id : false;
        } else {
            // 新增
            unset($data['id']);
            return $this->insert($data);
        }
    }

    /**
     * 保存简历详细信息
     * 
     * @param int $resumeId 简历ID
     * @param array $data 详细信息数据
     * @return bool
     */
    public function saveResumeDetail($resumeId, $data)
    {
        $detailTable = $this->perfix . 'resume_detail';
        $data['resume_id'] = $resumeId;

        // 检查是否已存在
        $exists = $this->db->getone($detailTable, "`resume_id`={$resumeId}");
        
        if ($exists) {
            // 更新
            unset($data['resume_id']);
            return $this->db->update($detailTable, $data, "`resume_id`={$resumeId}");
        } else {
            // 新增
            return $this->db->insert($detailTable, $data);
        }
    }

    /**
     * 检查手机号是否已存在
     * 
     * @param string $mobile 手机号
     * @param int $excludeId 排除的ID
     * @return bool
     */
    public function checkMobileExists($mobile, $excludeId = 0)
    {
        $where = "`mobile`='{$mobile}'";
        if ($excludeId > 0) {
            $where .= " AND `id`<>{$excludeId}";
        }
        
        return $this->rows($where) > 0;
    }

    /**
     * 审核简历
     * 
     * @param int $id 简历ID
     * @param int $status 审核状态 1通过 2拒绝
     * @param string $remark 审核备注
     * @return bool
     */
    public function reviewResume($id, $status, $remark = '')
    {
        $data = array(
            'status' => $status,
            'review_time' => $this->rock->now,
            'reviewer_id' => $this->adminid,
            'reviewer_name' => $this->adminname,
            'remark' => $remark,
            'optdt' => $this->rock->now,
            'optid' => $this->adminid,
            'optname' => $this->adminname
        );

        // 记录操作日志
        $this->addResumeLog($id, 'review', null, $status, $remark);

        return $this->update($data, $id);
    }

    /**
     * 添加简历操作日志
     * 
     * @param int $resumeId 简历ID
     * @param string $action 操作类型
     * @param int $oldStatus 原状态
     * @param int $newStatus 新状态
     * @param string $remark 备注
     * @return bool
     */
    public function addResumeLog($resumeId, $action, $oldStatus = null, $newStatus = null, $remark = '')
    {
        $logTable = $this->perfix . 'resume_log';
        $data = array(
            'resume_id' => $resumeId,
            'action' => $action,
            'old_status' => $oldStatus,
            'new_status' => $newStatus,
            'remark' => $remark,
            'optdt' => $this->rock->now,
            'optid' => $this->adminid,
            'optname' => $this->adminname,
            'ip_address' => $this->rock->ip
        );

        return $this->db->insert($logTable, $data);
    }

    /**
     * 获取简历统计数据
     * 
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public function getResumeStats($startDate = '', $endDate = '')
    {
        $where = '1=1';
        if (!empty($startDate)) {
            $where .= " AND DATE(submit_time) >= '{$startDate}'";
        }
        if (!empty($endDate)) {
            $where .= " AND DATE(submit_time) <= '{$endDate}'";
        }

        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status=0 THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status=1 THEN 1 ELSE 0 END) as passed,
                    SUM(CASE WHEN status=2 THEN 1 ELSE 0 END) as rejected,
                    SUM(CASE WHEN status=3 THEN 1 ELSE 0 END) as hired
                FROM `{$this->table}` WHERE {$where}";

        return $this->db->getone($sql);
    }

    /**
     * 获取按日期分组的统计数据
     * 
     * @param string $startDate 开始日期
     * @param string $endDate 结束日期
     * @return array
     */
    public function getResumeStatsByDate($startDate = '', $endDate = '')
    {
        $where = '1=1';
        if (!empty($startDate)) {
            $where .= " AND DATE(submit_time) >= '{$startDate}'";
        }
        if (!empty($endDate)) {
            $where .= " AND DATE(submit_time) <= '{$endDate}'";
        }

        $sql = "SELECT 
                    DATE(submit_time) as date,
                    COUNT(*) as total,
                    SUM(CASE WHEN status=0 THEN 1 ELSE 0 END) as pending,
                    SUM(CASE WHEN status=1 THEN 1 ELSE 0 END) as passed,
                    SUM(CASE WHEN status=2 THEN 1 ELSE 0 END) as rejected
                FROM `{$this->table}` 
                WHERE {$where}
                GROUP BY DATE(submit_time)
                ORDER BY DATE(submit_time) DESC";

        return $this->db->getall($sql);
    }

    /**
     * 转换为员工档案
     * 
     * @param int $resumeId 简历ID
     * @param array $employeeData 员工数据
     * @return bool
     */
    public function convertToEmployee($resumeId, $employeeData = array())
    {
        $resume = $this->getResumeDetail($resumeId);
        if (!$resume) {
            return false;
        }

        // 检查手机号在admin表中是否已存在
        $adminModel = m('admin');
        if ($adminModel->rows("`mobile`='{$resume['mobile']}'") > 0) {
            return false; // 手机号已存在
        }

        // 创建admin表记录
        $adminData = array(
            'name' => $resume['name'],
            'mobile' => $resume['mobile'],
            'email' => $resume['email'],
            'ranking' => $resume['position'],
            'sex' => $resume['gender'] == 1 ? '男' : ($resume['gender'] == 2 ? '女' : ''),
            'status' => 1,
            'workdate' => date('Y-m-d'),
            'regdt' => $this->rock->now
        );

        // 合并额外的员工数据
        $adminData = array_merge($adminData, $employeeData);

        $userId = $adminModel->insert($adminData);
        if (!$userId) {
            return false;
        }

        // 创建userinfo表记录
        $userinfoModel = m('userinfo');
        $userinfoData = array(
            'id' => $userId,
            'name' => $resume['name'],
            'ranking' => $resume['position']
        );
        $userinfoModel->insert($userinfoData);

        // 更新简历状态为已入职
        $this->update(array('status' => 3), $resumeId);

        // 记录操作日志
        $this->addResumeLog($resumeId, 'convert_to_employee', $resume['status'], 3, "转换为员工档案，用户ID：{$userId}");

        return $userId;
    }

    /**
     * 获取状态文本
     * 
     * @param int $status 状态值
     * @return string
     */
    public function getStatusText($status)
    {
        $statusMap = array(
            0 => '待审核',
            1 => '通过',
            2 => '拒绝',
            3 => '已入职'
        );

        return isset($statusMap[$status]) ? $statusMap[$status] : '未知';
    }

    /**
     * 获取性别文本
     * 
     * @param int $gender 性别值
     * @return string
     */
    public function getGenderText($gender)
    {
        $genderMap = array(
            1 => '男',
            2 => '女'
        );

        return isset($genderMap[$gender]) ? $genderMap[$gender] : '';
    }
}
