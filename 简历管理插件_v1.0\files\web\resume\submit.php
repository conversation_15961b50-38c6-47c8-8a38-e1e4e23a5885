<?php
/**
 * 简历提交处理文件
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '请求方法不允许']);
    exit;
}

// 引入信呼系统核心文件
require_once '../../../xinhu/webmain/model/base.php';

/**
 * 简历提交处理类
 */
class ResumeSubmitHandler
{
    private $db;
    private $config;
    private $uploadDir;
    
    public function __construct()
    {
        // 初始化数据库连接
        $this->initDatabase();
        
        // 设置上传目录
        $this->uploadDir = '../../../xinhu/upload/resume/' . date('Y-m') . '/';
        if (!is_dir($this->uploadDir)) {
            mkdir($this->uploadDir, 0755, true);
        }
        
        // 加载配置
        $this->loadConfig();
    }
    
    /**
     * 初始化数据库连接
     */
    private function initDatabase()
    {
        try {
            global $rock;
            $this->db = $rock->db;
        } catch (Exception $e) {
            $this->returnError('系统初始化失败');
        }
    }
    
    /**
     * 加载配置
     */
    private function loadConfig()
    {
        $this->config = [
            'max_file_size' => 5 * 1024 * 1024, // 5MB
            'allowed_types' => ['pdf', 'doc', 'docx'],
            'rate_limit' => 10, // 每小时最多提交10次
        ];
    }
    
    /**
     * 处理简历提交
     */
    public function handleSubmit()
    {
        try {
            // 安全检查
            $this->securityCheck();
            
            // 验证数据
            $data = $this->validateData();
            
            // 处理文件上传
            $filePath = $this->handleFileUpload();
            if ($filePath) {
                $data['resume_file'] = $filePath;
            }
            
            // 保存到数据库
            $resumeId = $this->saveResume($data);
            
            if ($resumeId) {
                // 记录提交日志
                $this->logSubmit($resumeId);
                
                // 发送通知（可选）
                $this->sendNotification($data);
                
                $this->returnSuccess('简历提交成功，我们会尽快与您联系！', $resumeId);
            } else {
                $this->returnError('提交失败，请重试');
            }
            
        } catch (Exception $e) {
            $this->returnError($e->getMessage());
        }
    }
    
    /**
     * 安全检查
     */
    private function securityCheck()
    {
        $ip = $this->getClientIP();
        
        // IP访问频率限制
        $recentSubmits = $this->db->getone(
            "SELECT COUNT(*) as count FROM xinhu_resume 
             WHERE ip_address = '{$ip}' AND submit_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)"
        );
        
        if ($recentSubmits && $recentSubmits['count'] >= $this->config['rate_limit']) {
            throw new Exception('提交过于频繁，请稍后再试');
        }
        
        // 检查是否为恶意请求
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($userAgent) || strlen($userAgent) < 10) {
            throw new Exception('请求异常');
        }
    }
    
    /**
     * 验证提交数据
     */
    private function validateData()
    {
        $data = [];
        
        // 必填字段验证
        $requiredFields = ['name', 'mobile', 'position'];
        foreach ($requiredFields as $field) {
            $value = trim($_POST[$field] ?? '');
            if (empty($value)) {
                throw new Exception("请填写{$this->getFieldName($field)}");
            }
            $data[$field] = $this->sanitizeInput($value);
        }
        
        // 手机号格式验证
        if (!preg_match('/^1[3-9]\d{9}$/', $data['mobile'])) {
            throw new Exception('请填写正确的手机号');
        }
        
        // 检查手机号是否已存在
        $existingResume = $this->db->getone(
            "SELECT id FROM xinhu_resume WHERE mobile = '{$data['mobile']}' AND status IN (0,1,3)"
        );
        if ($existingResume) {
            throw new Exception('该手机号已提交过简历');
        }
        
        // 可选字段处理
        $optionalFields = [
            'email', 'gender', 'birth_date', 'education', 'experience_years',
            'current_salary', 'expected_salary', 'available_date',
            'work_experience', 'education_background', 'skills', 'self_introduction',
            'emergency_contact', 'emergency_phone', 'address'
        ];
        
        foreach ($optionalFields as $field) {
            $value = trim($_POST[$field] ?? '');
            if (!empty($value)) {
                $data[$field] = $this->sanitizeInput($value);
            }
        }
        
        // 邮箱格式验证
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('请填写正确的邮箱地址');
        }
        
        // 设置默认值
        $data['submit_time'] = date('Y-m-d H:i:s');
        $data['ip_address'] = $this->getClientIP();
        $data['user_agent'] = $_SERVER['HTTP_USER_AGENT'] ?? '';
        $data['source'] = 'web';
        $data['status'] = 0; // 待审核
        
        return $data;
    }
    
    /**
     * 处理文件上传
     */
    private function handleFileUpload()
    {
        if (!isset($_FILES['resume_file']) || $_FILES['resume_file']['error'] === UPLOAD_ERR_NO_FILE) {
            return null;
        }
        
        $file = $_FILES['resume_file'];
        
        // 检查上传错误
        if ($file['error'] !== UPLOAD_ERR_OK) {
            throw new Exception('文件上传失败');
        }
        
        // 检查文件大小
        if ($file['size'] > $this->config['max_file_size']) {
            throw new Exception('文件大小不能超过5MB');
        }
        
        // 检查文件类型
        $fileInfo = pathinfo($file['name']);
        $extension = strtolower($fileInfo['extension'] ?? '');
        
        if (!in_array($extension, $this->config['allowed_types'])) {
            throw new Exception('只支持PDF、DOC、DOCX格式的文件');
        }
        
        // 生成安全的文件名
        $fileName = date('YmdHis') . '_' . uniqid() . '.' . $extension;
        $filePath = $this->uploadDir . $fileName;
        
        // 移动文件
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            throw new Exception('文件保存失败');
        }
        
        return $filePath;
    }
    
    /**
     * 保存简历到数据库
     */
    private function saveResume($data)
    {
        // 分离基本信息和详细信息
        $basicFields = ['name', 'mobile', 'email', 'position', 'resume_file', 'status', 
                       'submit_time', 'ip_address', 'user_agent', 'source'];
        $detailFields = ['gender', 'birth_date', 'education', 'experience_years',
                        'current_salary', 'expected_salary', 'available_date',
                        'work_experience', 'education_background', 'skills', 'self_introduction',
                        'emergency_contact', 'emergency_phone', 'address'];
        
        // 准备基本信息数据
        $basicData = [];
        foreach ($basicFields as $field) {
            if (isset($data[$field])) {
                $basicData[$field] = $data[$field];
            }
        }
        
        // 插入基本信息
        $resumeId = $this->db->insert('xinhu_resume', $basicData);
        
        if ($resumeId) {
            // 准备详细信息数据
            $detailData = ['resume_id' => $resumeId];
            foreach ($detailFields as $field) {
                if (isset($data[$field])) {
                    $detailData[$field] = $data[$field];
                }
            }
            
            // 插入详细信息
            if (!empty($detailData) && count($detailData) > 1) {
                $this->db->insert('xinhu_resume_detail', $detailData);
            }
        }
        
        return $resumeId;
    }
    
    /**
     * 记录提交日志
     */
    private function logSubmit($resumeId)
    {
        $logData = [
            'resume_id' => $resumeId,
            'action' => 'submit',
            'new_status' => 0,
            'remark' => '简历提交',
            'optdt' => date('Y-m-d H:i:s'),
            'ip_address' => $this->getClientIP()
        ];
        
        $this->db->insert('xinhu_resume_log', $logData);
    }
    
    /**
     * 发送通知（可选实现）
     */
    private function sendNotification($data)
    {
        // 这里可以实现邮件通知、短信通知等功能
        // 暂时留空，后续可以扩展
    }
    
    /**
     * 获取客户端IP
     */
    private function getClientIP()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 清理输入数据
     */
    private function sanitizeInput($input)
    {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    /**
     * 获取字段中文名
     */
    private function getFieldName($field)
    {
        $fieldNames = [
            'name' => '姓名',
            'mobile' => '手机号',
            'position' => '应聘职位',
            'email' => '邮箱'
        ];
        
        return $fieldNames[$field] ?? $field;
    }
    
    /**
     * 返回成功响应
     */
    private function returnSuccess($message, $data = null)
    {
        $response = ['success' => true, 'message' => $message];
        if ($data !== null) {
            $response['data'] = $data;
        }
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 返回错误响应
     */
    private function returnError($message)
    {
        http_response_code(400);
        echo json_encode(['success' => false, 'message' => $message], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 处理请求
try {
    $handler = new ResumeSubmitHandler();
    $handler->handleSubmit();
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试'], JSON_UNESCAPED_UNICODE);
}
