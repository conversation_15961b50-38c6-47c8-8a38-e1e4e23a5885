<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- 禁止目录浏览 -->
        <directoryBrowse enabled="false" />
        
        <!-- 安全配置 -->
        <security>
            <requestFiltering>
                <!-- 只允许特定文件类型 -->
                <fileExtensions allowUnlisted="false">
                    <add fileExtension=".pdf" allowed="true" />
                    <add fileExtension=".doc" allowed="true" />
                    <add fileExtension=".docx" allowed="true" />
                </fileExtensions>
                
                <!-- 禁止执行脚本文件 -->
                <denyUrlSequences>
                    <add sequence=".php" />
                    <add sequence=".phtml" />
                    <add sequence=".php3" />
                    <add sequence=".php4" />
                    <add sequence=".php5" />
                    <add sequence=".pl" />
                    <add sequence=".py" />
                    <add sequence=".jsp" />
                    <add sequence=".asp" />
                    <add sequence=".sh" />
                    <add sequence=".cgi" />
                </denyUrlSequences>
                
                <!-- 禁止访问隐藏文件 -->
                <hiddenSegments>
                    <add segment="." />
                </hiddenSegments>
            </requestFiltering>
        </security>
        
        <!-- 设置文件下载而非执行 -->
        <staticContent>
            <mimeMap fileExtension=".pdf" mimeType="application/pdf" />
            <mimeMap fileExtension=".doc" mimeType="application/msword" />
            <mimeMap fileExtension=".docx" mimeType="application/vnd.openxmlformats-officedocument.wordprocessingml.document" />
        </staticContent>
        
        <!-- 强制下载 -->
        <httpProtocol>
            <customHeaders>
                <add name="Content-Disposition" value="attachment" />
            </customHeaders>
        </httpProtocol>
    </system.webServer>
</configuration>
