# Windows环境安装说明

## 🖥️ Windows环境特别说明

本插件已针对Windows环境进行优化，支持以下Windows服务器环境：
- Windows Server 2012/2016/2019/2022
- Windows 10/11（开发测试环境）
- Apache for Windows 2.4 或更高版本

## 📋 Windows环境要求

### 1. 系统要求
- **操作系统**: Windows Server 2012 或更高版本
- **Web服务器**: Apache 2.4 或更高版本
- **PHP版本**: 7.0 或更高版本
- **数据库**: MySQL 5.6 或更高版本

### 2. PHP扩展检查
在Windows环境中检查PHP扩展：
```cmd
# 命令行检查
php -m | findstr "pdo pdo_mysql gd fileinfo json"

# 或创建PHP文件检查
echo "<?php phpinfo(); ?>" > check.php
```

必需的PHP扩展：
- `php_pdo.dll`
- `php_pdo_mysql.dll`
- `php_gd2.dll`
- `php_fileinfo.dll`
- `php_json.dll`

### 3. 目录权限设置

**Windows Apache环境权限设置：**
1. 右键点击网站根目录 → 属性 → 安全
2. 添加 `Everyone` 或 `Users` 用户组
3. 给予以下权限：
   - `webmain\main` - 完全控制
   - `web` - 完全控制
   - `upload` - 完全控制

**命令行设置权限：**
```cmd
# 给予Everyone用户组完全控制权限
icacls "C:\Apache24\htdocs\xinhu\webmain\main" /grant Everyone:(OI)(CI)F
icacls "C:\Apache24\htdocs\xinhu\web" /grant Everyone:(OI)(CI)F
icacls "C:\Apache24\htdocs\xinhu\upload" /grant Everyone:(OI)(CI)F
```

## 🚀 Windows安装步骤

### 方法一：Apache环境安装（推荐）

1. **解压到Apache目录**
   ```cmd
   # 假设Apache安装在C:\Apache24
   7z x 简历管理插件_v1.0.zip -o"C:\Apache24\htdocs\xinhu\plugins\"
   ```

2. **设置目录权限**
   ```cmd
   # 给予Everyone权限（需要管理员权限）
   icacls "C:\Apache24\htdocs\xinhu\webmain\main" /grant Everyone:(OI)(CI)F
   icacls "C:\Apache24\htdocs\xinhu\web" /grant Everyone:(OI)(CI)F
   icacls "C:\Apache24\htdocs\xinhu\upload" /grant Everyone:(OI)(CI)F
   ```

3. **检查Apache配置**
   - 确保Apache已启用mod_rewrite模块
   - 检查httpd.conf中AllowOverride设置为All

4. **执行安装**
   - 在浏览器中访问：
   ```
   http://localhost/xinhu/plugins/简历管理插件_v1.0/安装脚本.php
   ```

### 方法二：XAMPP环境安装

1. **解压到XAMPP目录**
   ```cmd
   # XAMPP默认安装路径
   7z x 简历管理插件_v1.0.zip -o"C:\xampp\htdocs\xinhu\plugins\"
   ```

2. **启动Apache和MySQL**
   - 打开XAMPP控制面板
   - 启动Apache和MySQL服务

3. **执行安装**
   - 访问安装脚本完成安装

## ⚙️ Apache特殊配置

### 1. mod_rewrite模块
确保Apache启用了mod_rewrite模块：
1. 编辑 `httpd.conf` 文件
2. 取消注释：`LoadModule rewrite_module modules/mod_rewrite.so`
3. 重启Apache服务

### 2. .htaccess支持
确保目录支持.htaccess：
```apache
# 在httpd.conf中设置
<Directory "C:/Apache24/htdocs">
    AllowOverride All
    Require all granted
</Directory>
```

### 3. PHP配置
确保Apache中PHP配置正确：
```apache
# 在httpd.conf中添加
LoadModule php_module "C:/php/php8apache2_4.dll"
AddType application/x-httpd-php .php
PHPIniDir "C:/php"
```

## 🔧 Windows路径处理

### 1. 路径分隔符
插件已自动处理Windows路径分隔符：
- 支持正斜杠 `/` 和反斜杠 `\`
- 自动转换为系统适配的路径格式

### 2. 文件上传路径
默认上传路径：`upload\resume\`
可在配置中修改：
```json
{
    "upload": {
        "upload_path": "upload\\custom_resume\\",
        "windows_path": "D:\\uploads\\resume\\"
    }
}
```

## 🛡️ Windows安全配置

### 1. 文件权限
- 上传目录：只允许读写，禁止执行
- Web目录：允许读取HTML/PHP，禁止其他文件类型
- 配置目录：禁止Web访问

### 2. Apache安全设置
```apache
# .htaccess安全配置
<Files ~ "\.(php|html)$">
    Order allow,deny
    Allow from all
</Files>

<Files ~ "\.(txt|log|sql|md|json)$">
    Order allow,deny
    Deny from all
</Files>
```

## 🧪 Windows环境测试

### 1. 功能测试清单
- [ ] 插件安装成功
- [ ] 后台菜单显示正常
- [ ] 外链页面可访问
- [ ] 文件上传功能正常
- [ ] 数据库操作正常
- [ ] 权限控制生效

### 2. 性能测试
```cmd
# 使用Windows性能监视器
perfmon.exe

# 监控指标：
# - CPU使用率
# - 内存使用量
# - 磁盘I/O
# - 网络流量
```

## ❌ Windows常见问题

### 1. 权限问题
**问题**: 文件上传失败，提示权限不足
**解决**: 
```cmd
# 检查目录权限
icacls "upload\resume" /T
# 修复权限
icacls "upload\resume" /grant Everyone:(OI)(CI)F /T
```

### 2. 路径问题
**问题**: 文件路径错误，找不到文件
**解决**: 检查路径分隔符，使用绝对路径

### 3. PHP扩展问题
**问题**: 缺少PHP扩展
**解决**: 
```ini
# 在php.ini中启用扩展
extension=pdo_mysql
extension=gd2
extension=fileinfo
```

### 4. Apache配置问题
**问题**: .htaccess不生效
**解决**: 检查Apache配置，确保AllowOverride设置为All

## 📞 Windows技术支持

### 常用工具
- **事件查看器**: 查看系统和应用程序日志
- **Apache监控**: 查看Apache访问和错误日志
- **性能监视器**: 监控系统性能
- **任务管理器**: 查看进程和资源使用

### 日志位置
- **Apache访问日志**: `C:\Apache24\logs\access.log`
- **Apache错误日志**: `C:\Apache24\logs\error.log`
- **PHP错误日志**: 根据php.ini配置
- **应用程序日志**: Windows事件查看器

### 获取帮助
- 邮箱: <EMAIL>
- 提供Windows版本、Apache版本、PHP版本信息
- 附上相关错误日志

---

**Windows安装说明版本**: 1.0.0  
**适用系统**: Windows Server 2012+, Windows 10+  
**更新日期**: 2025-06-21
