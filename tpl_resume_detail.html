<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{$title}</title>
    <style>
        .resume-detail {
            max-width: 900px;
            margin: 20px auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .resume-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .resume-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
        }
        .resume-header .status {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            background: rgba(255,255,255,0.2);
            font-size: 14px;
        }
        .resume-content {
            padding: 30px;
        }
        .info-section {
            margin-bottom: 30px;
        }
        .section-title {
            color: #667eea;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .info-item {
            display: flex;
            align-items: center;
        }
        .info-label {
            font-weight: 600;
            color: #333;
            min-width: 100px;
            margin-right: 10px;
        }
        .info-value {
            color: #666;
            flex: 1;
        }
        .text-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
            margin-top: 10px;
            line-height: 1.6;
        }
        .file-download {
            display: inline-block;
            padding: 8px 16px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            transition: background 0.3s;
        }
        .file-download:hover {
            background: #5a67d8;
            color: white;
            text-decoration: none;
        }
        .action-buttons {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #eee;
        }
        .btn {
            margin: 0 5px;
        }
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #667eea;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-item::before {
            content: '';
            position: absolute;
            left: -19px;
            top: 5px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #667eea;
        }
        .timeline-time {
            font-size: 12px;
            color: #999;
        }
        .timeline-content {
            margin-top: 5px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="resume-detail">
        <!-- 简历头部 -->
        <div class="resume-header">
            <h1>{$resume.name}</h1>
            <p>{$resume.position} | {$resume.mobile}</p>
            <span class="status">{$resume.status_text}</span>
        </div>

        <!-- 简历内容 -->
        <div class="resume-content">
            <!-- 基本信息 -->
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-user"></i> 基本信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">姓名：</span>
                        <span class="info-value">{$resume.name}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">性别：</span>
                        <span class="info-value">{$resume.gender_text|default:'未填写'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">手机号：</span>
                        <span class="info-value">{$resume.mobile}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">邮箱：</span>
                        <span class="info-value">{$resume.email|default:'未填写'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">出生日期：</span>
                        <span class="info-value">{$resume.birth_date|default:'未填写'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">学历：</span>
                        <span class="info-value">{$resume.education|default:'未填写'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">现居住地：</span>
                        <span class="info-value">{$resume.address|default:'未填写'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">婚姻状况：</span>
                        <span class="info-value">
                            {if $resume.marital_status == 1}未婚
                            {elseif $resume.marital_status == 2}已婚
                            {elseif $resume.marital_status == 3}离异
                            {else}未填写{/if}
                        </span>
                    </div>
                </div>
            </div>

            <!-- 求职信息 -->
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-briefcase"></i> 求职信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">应聘职位：</span>
                        <span class="info-value">{$resume.position}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">工作年限：</span>
                        <span class="info-value">
                            {if $resume.experience_years == 0}应届毕业生
                            {elseif $resume.experience_years}
                                {$resume.experience_years}年
                            {else}未填写{/if}
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">当前薪资：</span>
                        <span class="info-value">
                            {if $resume.current_salary}{$resume.current_salary}元{else}未填写{/if}
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">期望薪资：</span>
                        <span class="info-value">
                            {if $resume.expected_salary}{$resume.expected_salary}元{else}未填写{/if}
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">可到岗日期：</span>
                        <span class="info-value">{$resume.available_date|default:'未填写'}</span>
                    </div>
                </div>
            </div>

            <!-- 工作经历 -->
            {if $resume.work_experience}
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-history"></i> 工作经历</h3>
                <div class="text-content">
                    {$resume.work_experience|nl2br}
                </div>
            </div>
            {/if}

            <!-- 教育背景 -->
            {if $resume.education_background}
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-graduation-cap"></i> 教育背景</h3>
                <div class="text-content">
                    {$resume.education_background|nl2br}
                </div>
            </div>
            {/if}

            <!-- 技能特长 -->
            {if $resume.skills}
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-star"></i> 技能特长</h3>
                <div class="text-content">
                    {$resume.skills|nl2br}
                </div>
            </div>
            {/if}

            <!-- 自我介绍 -->
            {if $resume.self_introduction}
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-comment"></i> 自我介绍</h3>
                <div class="text-content">
                    {$resume.self_introduction|nl2br}
                </div>
            </div>
            {/if}

            <!-- 简历文件 -->
            {if $resume.resume_file}
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-file"></i> 简历文件</h3>
                <a href="{$resume.resume_file}" class="file-download" target="_blank">
                    <i class="fa fa-download"></i> 下载简历文件
                </a>
            </div>
            {/if}

            <!-- 紧急联系人 -->
            {if $resume.emergency_contact || $resume.emergency_phone}
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-phone"></i> 紧急联系人</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">联系人：</span>
                        <span class="info-value">{$resume.emergency_contact|default:'未填写'}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">联系电话：</span>
                        <span class="info-value">{$resume.emergency_phone|default:'未填写'}</span>
                    </div>
                </div>
            </div>
            {/if}

            <!-- 提交信息 -->
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-info-circle"></i> 提交信息</h3>
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-time">{$resume.submit_time}</div>
                        <div class="timeline-content">简历提交</div>
                    </div>
                    {if $resume.review_time}
                    <div class="timeline-item">
                        <div class="timeline-time">{$resume.review_time}</div>
                        <div class="timeline-content">
                            审核{$resume.status_text} - {$resume.reviewer_name}
                            {if $resume.remark}
                            <br><small>备注：{$resume.remark}</small>
                            {/if}
                        </div>
                    </div>
                    {/if}
                </div>
            </div>

            <!-- 其他信息 -->
            {if $resume.other_info}
            <div class="info-section">
                <h3 class="section-title"><i class="fa fa-ellipsis-h"></i> 其他信息</h3>
                <div class="text-content">
                    {$resume.other_info|nl2br}
                </div>
            </div>
            {/if}
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
            {if $resume.status == 0}
            <button type="button" class="btn btn-success" onclick="reviewResume({$resume.id}, 1)">
                <i class="fa fa-check"></i> 审核通过
            </button>
            <button type="button" class="btn btn-warning" onclick="reviewResume({$resume.id}, 2)">
                <i class="fa fa-times"></i> 审核拒绝
            </button>
            {/if}
            
            {if $resume.status == 1}
            <button type="button" class="btn btn-primary" onclick="convertToEmployee({$resume.id})">
                <i class="fa fa-user-plus"></i> 转为员工档案
            </button>
            {/if}
            
            <button type="button" class="btn btn-info" onclick="printResume()">
                <i class="fa fa-print"></i> 打印简历
            </button>
            
            <button type="button" class="btn btn-default" onclick="window.close()">
                <i class="fa fa-close"></i> 关闭
            </button>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="reviewModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">简历审核</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="reviewForm">
                        <input type="hidden" id="reviewResumeId">
                        <input type="hidden" id="reviewStatus">
                        <div class="form-group">
                            <label>审核结果</label>
                            <div id="reviewStatusText" class="form-control-static"></div>
                        </div>
                        <div class="form-group">
                            <label>审核备注</label>
                            <textarea class="form-control" id="reviewRemark" rows="3" placeholder="请填写审核意见..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitReview()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 审核简历
        function reviewResume(id, status) {
            $('#reviewResumeId').val(id);
            $('#reviewStatus').val(status);
            $('#reviewStatusText').text(status == 1 ? '通过' : '拒绝');
            $('#reviewRemark').val('');
            $('#reviewModal').modal('show');
        }

        // 提交审核
        function submitReview() {
            var id = $('#reviewResumeId').val();
            var status = $('#reviewStatus').val();
            var remark = $('#reviewRemark').val();
            
            $.post('?a=review', {
                id: id,
                status: status,
                remark: remark
            }, function(response) {
                if (response.success) {
                    alert(response.msg);
                    $('#reviewModal').modal('hide');
                    location.reload();
                } else {
                    alert(response.msg || '审核失败');
                }
            }, 'json');
        }

        // 转换为员工档案
        function convertToEmployee(id) {
            if (confirm('确定要将此简历转换为员工档案吗？')) {
                $.post('?a=convertToEmployee', {id: id}, function(response) {
                    if (response.success) {
                        alert(response.msg);
                        location.reload();
                    } else {
                        alert(response.msg || '转换失败');
                    }
                }, 'json');
            }
        }

        // 打印简历
        function printResume() {
            window.print();
        }
    </script>
</body>
</html>
