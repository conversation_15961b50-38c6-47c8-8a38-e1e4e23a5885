-- ========================================
-- 信呼系统简历管理插件数据库安装脚本
-- 版本：1.0.0
-- 创建时间：2025-06-21
-- 说明：包含数据表创建、菜单插入、权限配置
-- ========================================

-- 检查系统版本兼容性
SET @version = (SELECT `value` FROM `[Q]option` WHERE `name`='version' LIMIT 1);
-- 如果需要版本检查，可以在这里添加条件判断

ROCKSPLIT

-- ========================================
-- 1. 创建简历管理主表
-- ========================================
CREATE TABLE IF NOT EXISTS `[Q]resume` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `mobile` varchar(50) NOT NULL COMMENT '手机号',
  `email` varchar(200) DEFAULT NULL COMMENT '邮箱',
  `position` varchar(50) DEFAULT NULL COMMENT '应聘职位',
  `resume_file` varchar(200) DEFAULT NULL COMMENT '简历文件路径',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态:0待审核,1通过,2拒绝,3已入职',
  `submit_time` datetime DEFAULT NULL COMMENT '提交时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewer_id` int(11) DEFAULT '0' COMMENT '审核人ID',
  `reviewer_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `remark` text COMMENT '备注',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '提交IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '浏览器信息',
  `source` varchar(50) DEFAULT 'web' COMMENT '来源:web,mobile,api',
  `priority` tinyint(4) DEFAULT '0' COMMENT '优先级:0普通,1重要,2紧急',
  `optdt` datetime DEFAULT NULL COMMENT '操作时间',
  `optid` int(11) DEFAULT '0' COMMENT '操作人ID',
  `optname` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  PRIMARY KEY (`id`),
  KEY `mobile` (`mobile`),
  KEY `status` (`status`),
  KEY `submit_time` (`submit_time`),
  KEY `position` (`position`),
  KEY `reviewer_id` (`reviewer_id`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='简历管理主表';

ROCKSPLIT

-- ========================================
-- 2. 创建简历详细信息表
-- ========================================
CREATE TABLE IF NOT EXISTS `[Q]resume_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `resume_id` int(11) NOT NULL COMMENT '简历ID',
  `gender` tinyint(4) DEFAULT NULL COMMENT '性别:1男,2女',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `education` varchar(20) DEFAULT NULL COMMENT '学历',
  `experience_years` tinyint(4) DEFAULT NULL COMMENT '工作年限',
  `current_salary` decimal(10,2) DEFAULT NULL COMMENT '当前薪资',
  `expected_salary` decimal(10,2) DEFAULT NULL COMMENT '期望薪资',
  `work_experience` text COMMENT '工作经历',
  `education_background` text COMMENT '教育背景',
  `skills` text COMMENT '技能特长',
  `self_introduction` text COMMENT '自我介绍',
  `emergency_contact` varchar(100) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(50) DEFAULT NULL COMMENT '紧急联系电话',
  `address` varchar(200) DEFAULT NULL COMMENT '现居住地址',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `marital_status` tinyint(4) DEFAULT NULL COMMENT '婚姻状况:1未婚,2已婚,3离异',
  `available_date` date DEFAULT NULL COMMENT '可到岗日期',
  `other_info` text COMMENT '其他信息',
  PRIMARY KEY (`id`),
  UNIQUE KEY `resume_id` (`resume_id`),
  KEY `gender` (`gender`),
  KEY `education` (`education`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='简历详细信息表';

ROCKSPLIT

-- ========================================
-- 3. 创建简历操作日志表
-- ========================================
CREATE TABLE IF NOT EXISTS `[Q]resume_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `resume_id` int(11) NOT NULL COMMENT '简历ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `old_status` tinyint(4) DEFAULT NULL COMMENT '原状态',
  `new_status` tinyint(4) DEFAULT NULL COMMENT '新状态',
  `remark` varchar(500) DEFAULT NULL COMMENT '操作备注',
  `optdt` datetime DEFAULT NULL COMMENT '操作时间',
  `optid` int(11) DEFAULT '0' COMMENT '操作人ID',
  `optname` varchar(50) DEFAULT NULL COMMENT '操作人姓名',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '操作IP',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`),
  KEY `action` (`action`),
  KEY `optdt` (`optdt`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='简历操作日志表';

ROCKSPLIT

-- ========================================
-- 4. 创建简历文件表
-- ========================================
CREATE TABLE IF NOT EXISTS `[Q]resume_files` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `resume_id` int(11) NOT NULL COMMENT '简历ID',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` int(11) DEFAULT '0' COMMENT '文件大小(字节)',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `upload_time` datetime DEFAULT NULL COMMENT '上传时间',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态:0删除,1正常',
  PRIMARY KEY (`id`),
  KEY `resume_id` (`resume_id`),
  KEY `status` (`status`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='简历文件表';

ROCKSPLIT

-- ========================================
-- 5. 查找人事档案父级菜单ID并插入简历管理菜单
-- ========================================
SET @parent_menu_id = (
  SELECT `pid` FROM `[Q]menu` 
  WHERE `url` LIKE '%userinfo%' AND `status`=1 
  ORDER BY `id` ASC LIMIT 1
);

-- 如果找不到父级菜单，使用默认值（通常人事档案的父级ID）
SET @parent_menu_id = IFNULL(@parent_menu_id, 85);

-- 插入简历管理主菜单
INSERT INTO `[Q]menu` (`name`,`pid`,`sort`,`url`,`icons`,`ispir`,`status`,`type`) 
SELECT '简历管理', @parent_menu_id, 999, 'main,resume', 'file-text-o', '1', '1', '0'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]menu` WHERE `name`='简历管理');

ROCKSPLIT

-- 获取刚插入的简历管理菜单ID
SET @resume_menu_id = (SELECT `id` FROM `[Q]menu` WHERE `name`='简历管理' LIMIT 1);

-- 插入简历管理子菜单
INSERT INTO `[Q]menu` (`name`,`pid`,`sort`,`url`,`icons`,`ispir`,`status`,`type`) 
SELECT '简历列表', @resume_menu_id, 1, 'main,resume,list', 'list', '1', '1', '0'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]menu` WHERE `name`='简历列表');

ROCKSPLIT

INSERT INTO `[Q]menu` (`name`,`pid`,`sort`,`url`,`icons`,`ispir`,`status`,`type`) 
SELECT '简历审核', @resume_menu_id, 2, 'main,resume,review', 'check', '1', '1', '0'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]menu` WHERE `name`='简历审核');

ROCKSPLIT

INSERT INTO `[Q]menu` (`name`,`pid`,`sort`,`url`,`icons`,`ispir`,`status`,`type`) 
SELECT '简历统计', @resume_menu_id, 3, 'main,resume,stats', 'bar-chart', '1', '1', '0'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]menu` WHERE `name`='简历统计');

ROCKSPLIT

-- ========================================
-- 6. 创建系统配置选项
-- ========================================
-- 获取系统选项父级ID
SET @sys_option_id = (SELECT `id` FROM `[Q]option` WHERE `num`='sysoption' LIMIT 1);

-- 插入简历管理相关配置
INSERT INTO `[Q]option` (`name`,`pid`,`value`,`num`,`explain`) 
SELECT '简历管理插件版本', @sys_option_id, '1.0.0', 'resume_plugin_version', '简历管理插件当前版本号'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]option` WHERE `num`='resume_plugin_version');

ROCKSPLIT

INSERT INTO `[Q]option` (`name`,`pid`,`value`,`num`,`explain`) 
SELECT '简历文件大小限制', @sys_option_id, '10', 'resume_file_size', '简历文件上传大小限制(MB)'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]option` WHERE `num`='resume_file_size');

ROCKSPLIT

INSERT INTO `[Q]option` (`name`,`pid`,`value`,`num`,`explain`) 
SELECT '简历文件类型限制', @sys_option_id, 'pdf,doc,docx,jpg,jpeg,png', 'resume_file_types', '允许上传的简历文件类型'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]option` WHERE `num`='resume_file_types');

ROCKSPLIT

INSERT INTO `[Q]option` (`name`,`pid`,`value`,`num`,`explain`) 
SELECT '简历外链访问限制', @sys_option_id, '1', 'resume_access_limit', '是否启用IP访问频率限制:0关闭,1启用'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]option` WHERE `num`='resume_access_limit');

ROCKSPLIT

INSERT INTO `[Q]option` (`name`,`pid`,`value`,`num`,`explain`) 
SELECT '简历访问频率限制', @sys_option_id, '10', 'resume_rate_limit', '每小时允许同一IP提交简历次数'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]option` WHERE `num`='resume_rate_limit');

ROCKSPLIT

-- ========================================
-- 7. 创建简历访问统计表
-- ========================================
CREATE TABLE IF NOT EXISTS `[Q]resume_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `date` date NOT NULL COMMENT '统计日期',
  `submit_count` int(11) DEFAULT '0' COMMENT '提交数量',
  `review_count` int(11) DEFAULT '0' COMMENT '审核数量',
  `pass_count` int(11) DEFAULT '0' COMMENT '通过数量',
  `reject_count` int(11) DEFAULT '0' COMMENT '拒绝数量',
  `visit_count` int(11) DEFAULT '0' COMMENT '访问次数',
  `unique_ip` int(11) DEFAULT '0' COMMENT '独立IP数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `date` (`date`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='简历访问统计表';

ROCKSPLIT

-- ========================================
-- 8. 创建插件管理表（如果不存在）
-- ========================================
CREATE TABLE IF NOT EXISTS `[Q]plugin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '插件标识',
  `display_name` varchar(200) NOT NULL COMMENT '插件显示名称',
  `version` varchar(20) NOT NULL COMMENT '插件版本',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态:0禁用,1启用',
  `install_time` datetime DEFAULT NULL COMMENT '安装时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `config` text COMMENT '插件配置',
  `install_log` text COMMENT '安装日志',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `status` (`status`)
) ENGINE=MyISAM AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='插件管理表';

ROCKSPLIT

-- ========================================
-- 9. 记录插件安装信息
-- ========================================
INSERT INTO `[Q]option` (`name`,`value`,`memo`) 
VALUES ('resume_plugin_install_time', NOW(), '简历管理插件安装时间')
ON DUPLICATE KEY UPDATE `value`=NOW();

ROCKSPLIT

INSERT INTO `[Q]option` (`name`,`value`,`memo`) 
VALUES ('resume_plugin_status', '1', '简历管理插件状态:0禁用,1启用')
ON DUPLICATE KEY UPDATE `value`='1';

ROCKSPLIT

-- ========================================
-- 安装脚本执行完成
-- ========================================
