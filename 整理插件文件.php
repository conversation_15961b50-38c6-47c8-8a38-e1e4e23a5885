<?php
/**
 * 简历管理插件文件自动整理脚本
 * 
 * 此脚本会自动创建标准插件目录结构，并移动重命名文件
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */

class PluginFileOrganizer
{
    private $sourceDir;
    private $targetDir;
    private $fileMap = [];
    private $log = [];
    
    public function __construct($sourceDir = '.', $targetDir = '简历管理插件_v1.0')
    {
        $this->sourceDir = rtrim($sourceDir, '/');
        $this->targetDir = $targetDir;
        $this->initFileMap();
    }
    
    /**
     * 初始化文件映射关系
     */
    private function initFileMap()
    {
        $this->fileMap = [
            // 核心配置文件
            'plugin.json' => '插件配置.json',
            'install.php' => '安装脚本.php',
            'uninstall.php' => '卸载脚本.php',
            
            // 文档文件
            'README_RESUME_MODULE.md' => '插件说明.md',
            'DEPLOYMENT_CHECKLIST.md' => 'docs/部署检查清单.md',
            'resume_plugin_structure.md' => 'docs/插件结构说明.md',
            'PLUGIN_PACKAGE_GUIDE.md' => 'docs/打包指南.md',
            'resume_module_design.md' => 'docs/架构设计.md',
            '简历管理插件整理说明.md' => 'docs/文件整理说明.md',
            
            // 数据库脚本
            'resume_upgrade.sql' => 'sql/安装脚本.sql',
            
            // 后端文件
            'resumeModel.php' => 'files/webmain/model/resumeModel.php',
            'resumeAction.php' => 'files/webmain/main/resume/resumeAction.php',
            'tpl_resume.html' => 'files/webmain/main/resume/tpl_resume.html',
            'tpl_resume_detail.html' => 'files/webmain/main/resume/tpl_resume_detail.html',
            
            // 工具类
            'resume_upload_handler.php' => 'files/webmain/include/ResumeUploadHandler.php',
            'resume_security.php' => 'files/webmain/include/ResumeSecurityManager.php',
            
            // 外链文件
            'resume_public_form.html' => 'files/web/resume/index.html',
            'resume_submit.php' => 'files/web/resume/submit.php',
            
            // 安装脚本
            'install_resume_module.php' => 'legacy/install_resume_module.php'
        ];
    }
    
    /**
     * 执行整理
     */
    public function organize()
    {
        try {
            $this->log('开始整理插件文件...');
            
            // 1. 创建目录结构
            $this->createDirectoryStructure();
            
            // 2. 移动和重命名文件
            $this->moveFiles();
            
            // 3. 创建保护文件
            $this->createProtectionFiles();
            
            // 4. 创建许可证和更新日志
            $this->createLicenseAndChangelog();
            
            // 5. 创建配置文件
            $this->createConfigFiles();
            
            // 6. 设置文件权限
            $this->setPermissions();
            
            // 7. 创建压缩包
            $this->createArchive();
            
            $this->log('插件文件整理完成！');
            return $this->getResult(true, '整理成功');
            
        } catch (Exception $e) {
            $this->log('整理失败: ' . $e->getMessage(), 'error');
            return $this->getResult(false, $e->getMessage());
        }
    }
    
    /**
     * 创建目录结构
     */
    private function createDirectoryStructure()
    {
        $this->log('创建目录结构...');
        
        $directories = [
            $this->targetDir,
            $this->targetDir . '/sql',
            $this->targetDir . '/files',
            $this->targetDir . '/files/webmain',
            $this->targetDir . '/files/webmain/model',
            $this->targetDir . '/files/webmain/main',
            $this->targetDir . '/files/webmain/main/resume',
            $this->targetDir . '/files/webmain/include',
            $this->targetDir . '/files/web',
            $this->targetDir . '/files/web/resume',
            $this->targetDir . '/files/web/resume/assets',
            $this->targetDir . '/files/web/resume/assets/css',
            $this->targetDir . '/files/web/resume/assets/js',
            $this->targetDir . '/files/web/resume/assets/images',
            $this->targetDir . '/files/upload',
            $this->targetDir . '/files/upload/resume',
            $this->targetDir . '/docs',
            $this->targetDir . '/config',
            $this->targetDir . '/tests',
            $this->targetDir . '/tests/unit',
            $this->targetDir . '/tests/integration',
            $this->targetDir . '/tests/fixtures',
            $this->targetDir . '/screenshots',
            $this->targetDir . '/legacy'
        ];
        
        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    throw new Exception('无法创建目录: ' . $dir);
                }
                $this->log('创建目录: ' . $dir);
            }
        }
    }
    
    /**
     * 移动文件
     */
    private function moveFiles()
    {
        $this->log('移动和重命名文件...');
        
        foreach ($this->fileMap as $source => $target) {
            $sourcePath = $this->sourceDir . '/' . $source;
            $targetPath = $this->targetDir . '/' . $target;
            
            if (file_exists($sourcePath)) {
                // 确保目标目录存在
                $targetDir = dirname($targetPath);
                if (!is_dir($targetDir)) {
                    mkdir($targetDir, 0755, true);
                }
                
                if (copy($sourcePath, $targetPath)) {
                    $this->log('复制文件: ' . $source . ' → ' . $target);
                } else {
                    $this->log('复制文件失败: ' . $source, 'warning');
                }
            } else {
                $this->log('源文件不存在: ' . $source, 'warning');
            }
        }
    }
    
    /**
     * 创建保护文件
     */
    private function createProtectionFiles()
    {
        $this->log('创建保护文件...');
        
        // 上传目录保护
        $htaccessContent = "Options -Indexes\n";
        $htaccessContent .= "Order allow,deny\n";
        $htaccessContent .= "Allow from all\n";
        $htaccessContent .= "<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n";
        $htaccessContent .= "    deny from all\n";
        $htaccessContent .= "</Files>\n";
        
        file_put_contents($this->targetDir . '/files/upload/resume/.htaccess', $htaccessContent);
        
        $indexContent = '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>';
        file_put_contents($this->targetDir . '/files/upload/resume/index.html', $indexContent);
        
        // Web目录保护
        $webHtaccess = "<Files ~ \"\\.(php)$\">\n";
        $webHtaccess .= "    Order allow,deny\n";
        $webHtaccess .= "    Allow from all\n";
        $webHtaccess .= "</Files>\n";
        
        file_put_contents($this->targetDir . '/files/web/resume/.htaccess', $webHtaccess);
        
        $this->log('保护文件创建完成');
    }
    
    /**
     * 创建许可证和更新日志
     */
    private function createLicenseAndChangelog()
    {
        $this->log('创建许可证和更新日志...');
        
        // 许可证
        $license = "MIT License\n\n";
        $license .= "Copyright (c) 2025 信呼开发团队\n\n";
        $license .= "Permission is hereby granted, free of charge, to any person obtaining a copy\n";
        $license .= "of this software and associated documentation files (the \"Software\"), to deal\n";
        $license .= "in the Software without restriction, including without limitation the rights\n";
        $license .= "to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n";
        $license .= "copies of the Software, and to permit persons to whom the Software is\n";
        $license .= "furnished to do so, subject to the following conditions:\n\n";
        $license .= "The above copyright notice and this permission notice shall be included in all\n";
        $license .= "copies or substantial portions of the Software.\n\n";
        $license .= "THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n";
        $license .= "IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n";
        $license .= "FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n";
        $license .= "AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n";
        $license .= "LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n";
        $license .= "OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n";
        $license .= "SOFTWARE.\n";
        
        file_put_contents($this->targetDir . '/许可证.txt', $license);
        
        // 更新日志
        $changelog = "# 更新日志\n\n";
        $changelog .= "## [1.0.0] - 2025-06-21\n\n";
        $changelog .= "### 新增功能\n";
        $changelog .= "- ✅ 完整的简历管理功能\n";
        $changelog .= "- ✅ 外链简历填写页面\n";
        $changelog .= "- ✅ 后台管理和审核功能\n";
        $changelog .= "- ✅ 文件上传和安全控制\n";
        $changelog .= "- ✅ 数据统计和导出功能\n";
        $changelog .= "- ✅ 简历转员工档案功能\n";
        $changelog .= "- ✅ 完善的权限控制系统\n\n";
        $changelog .= "### 技术特性\n";
        $changelog .= "- 🔧 标准化插件结构\n";
        $changelog .= "- 🔧 自动安装和卸载\n";
        $changelog .= "- 🔧 安全防护机制\n";
        $changelog .= "- 🔧 响应式设计\n";
        $changelog .= "- 🔧 API接口支持\n\n";
        $changelog .= "### 安全性\n";
        $changelog .= "- 🛡️ IP访问频率限制\n";
        $changelog .= "- 🛡️ 文件上传安全检查\n";
        $changelog .= "- 🛡️ SQL注入防护\n";
        $changelog .= "- 🛡️ XSS攻击防护\n";
        $changelog .= "- 🛡️ CSRF令牌验证\n";
        
        file_put_contents($this->targetDir . '/更新日志.md', $changelog);
        
        $this->log('许可证和更新日志创建完成');
    }
    
    /**
     * 创建配置文件
     */
    private function createConfigFiles()
    {
        $this->log('创建配置文件...');
        
        // 菜单配置
        $menuConfig = [
            'menus' => [
                [
                    'name' => '简历管理',
                    'parent_name' => '人事档案',
                    'url' => 'main.php?c=resume&a=index',
                    'icon' => 'fa-file-text-o',
                    'sort' => 10,
                    'children' => [
                        [
                            'name' => '简历列表',
                            'url' => 'main.php?c=resume&a=index',
                            'icon' => 'fa-list',
                            'sort' => 1
                        ],
                        [
                            'name' => '简历统计',
                            'url' => 'main.php?c=resume&a=stats',
                            'icon' => 'fa-bar-chart',
                            'sort' => 2
                        ]
                    ]
                ]
            ]
        ];
        
        file_put_contents($this->targetDir . '/config/菜单配置.json', json_encode($menuConfig, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        
        // 权限配置
        $permissionConfig = [
            'permissions' => [
                'resume_view' => '查看简历',
                'resume_edit' => '编辑简历',
                'resume_delete' => '删除简历',
                'resume_audit' => '审核简历',
                'resume_export' => '导出简历',
                'resume_stats' => '查看统计'
            ]
        ];
        
        file_put_contents($this->targetDir . '/config/权限配置.json', json_encode($permissionConfig, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        
        // 默认设置
        $defaultSettings = [
            'upload' => [
                'max_file_size' => '10MB',
                'allowed_types' => ['pdf', 'doc', 'docx', 'jpg', 'png'],
                'upload_path' => 'upload/resume/'
            ],
            'security' => [
                'rate_limit' => 10,
                'rate_window' => 3600,
                'enable_captcha' => true
            ],
            'notification' => [
                'email_admin' => true,
                'email_hr' => true
            ]
        ];
        
        file_put_contents($this->targetDir . '/config/默认设置.json', json_encode($defaultSettings, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
        
        $this->log('配置文件创建完成');
    }
    
    /**
     * 设置文件权限
     */
    private function setPermissions()
    {
        $this->log('设置文件权限...');
        
        // 设置目录权限
        $this->chmodRecursive($this->targetDir, 0755, 'dir');
        
        // 设置文件权限
        $this->chmodRecursive($this->targetDir, 0644, 'file');
        
        // 设置可执行权限
        if (file_exists($this->targetDir . '/安装脚本.php')) {
            chmod($this->targetDir . '/安装脚本.php', 0755);
        }
        if (file_exists($this->targetDir . '/卸载脚本.php')) {
            chmod($this->targetDir . '/卸载脚本.php', 0755);
        }
        
        $this->log('文件权限设置完成');
    }
    
    /**
     * 递归设置权限
     */
    private function chmodRecursive($path, $mode, $type)
    {
        if (is_dir($path)) {
            if ($type == 'dir') {
                chmod($path, $mode);
            }
            
            $files = scandir($path);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $this->chmodRecursive($path . '/' . $file, $mode, $type);
                }
            }
        } else {
            if ($type == 'file') {
                chmod($path, $mode);
            }
        }
    }
    
    /**
     * 创建压缩包
     */
    private function createArchive()
    {
        $this->log('创建压缩包...');
        
        if (class_exists('ZipArchive')) {
            $zip = new ZipArchive();
            $zipFile = $this->targetDir . '.zip';
            
            if ($zip->open($zipFile, ZipArchive::CREATE | ZipArchive::OVERWRITE) === TRUE) {
                $this->addToZip($zip, $this->targetDir, '');
                $zip->close();
                $this->log('ZIP压缩包创建成功: ' . $zipFile);
            } else {
                $this->log('ZIP压缩包创建失败', 'warning');
            }
        } else {
            $this->log('ZipArchive扩展不可用，跳过压缩包创建', 'warning');
        }
    }
    
    /**
     * 添加文件到ZIP
     */
    private function addToZip($zip, $path, $relativePath)
    {
        if (is_dir($path)) {
            $files = scandir($path);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    $fullPath = $path . '/' . $file;
                    $relPath = $relativePath ? $relativePath . '/' . $file : $file;
                    
                    if (is_dir($fullPath)) {
                        $zip->addEmptyDir($relPath);
                        $this->addToZip($zip, $fullPath, $relPath);
                    } else {
                        $zip->addFile($fullPath, $relPath);
                    }
                }
            }
        }
    }
    
    /**
     * 添加日志
     */
    private function log($message, $level = 'info')
    {
        $this->log[] = [
            'time' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message
        ];
        
        echo "[" . date('H:i:s') . "] " . $message . "\n";
    }
    
    /**
     * 获取结果
     */
    private function getResult($success, $message)
    {
        return [
            'success' => $success,
            'message' => $message,
            'target_dir' => $this->targetDir,
            'log' => $this->log
        ];
    }
}

// 如果直接运行此脚本
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    echo "=== 简历管理插件文件整理工具 ===\n\n";
    
    try {
        $organizer = new PluginFileOrganizer();
        $result = $organizer->organize();
        
        echo "\n=== 整理结果 ===\n";
        echo "状态: " . ($result['success'] ? '成功' : '失败') . "\n";
        echo "消息: " . $result['message'] . "\n";
        echo "目标目录: " . $result['target_dir'] . "\n";
        
        if ($result['success']) {
            echo "\n✅ 插件文件整理完成！\n";
            echo "📦 插件包位置: " . $result['target_dir'] . ".zip\n";
            echo "📁 插件目录: " . $result['target_dir'] . "/\n";
            echo "\n下一步：\n";
            echo "1. 检查生成的插件目录结构\n";
            echo "2. 测试安装脚本\n";
            echo "3. 分发插件包\n";
        }
        
    } catch (Exception $e) {
        echo "❌ 整理失败: " . $e->getMessage() . "\n";
    }
}
