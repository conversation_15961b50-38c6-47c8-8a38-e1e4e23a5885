<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简历管理</title>
</head>
<body>
    <div id="resumeListPanel">
        <!-- 工具栏 -->
        <div class="panel-toolbar">
            <div class="btn-group">
                <button type="button" class="btn btn-primary" onclick="refreshResumeList()">
                    <i class="fa fa-refresh"></i> 刷新
                </button>
                <button type="button" class="btn btn-success" onclick="batchReview(1)">
                    <i class="fa fa-check"></i> 批量通过
                </button>
                <button type="button" class="btn btn-warning" onclick="batchReview(2)">
                    <i class="fa fa-times"></i> 批量拒绝
                </button>
                <button type="button" class="btn btn-info" onclick="exportResume()">
                    <i class="fa fa-download"></i> 导出
                </button>
            </div>
            
            <!-- 搜索区域 -->
            <div class="search-area">
                <div class="form-inline">
                    <div class="form-group">
                        <input type="text" class="form-control" id="searchKey" placeholder="姓名/手机号/邮箱">
                    </div>
                    <div class="form-group">
                        <select class="form-control" id="searchStatus">
                            <option value="">全部状态</option>
                            <option value="0">待审核</option>
                            <option value="1">通过</option>
                            <option value="2">拒绝</option>
                            <option value="3">已入职</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <input type="text" class="form-control" id="searchPosition" placeholder="应聘职位">
                    </div>
                    <div class="form-group">
                        <input type="date" class="form-control" id="searchStartDate" placeholder="开始日期">
                    </div>
                    <div class="form-group">
                        <input type="date" class="form-control" id="searchEndDate" placeholder="结束日期">
                    </div>
                    <button type="button" class="btn btn-default" onclick="searchResume()">
                        <i class="fa fa-search"></i> 搜索
                    </button>
                    <button type="button" class="btn btn-default" onclick="resetSearch()">
                        <i class="fa fa-refresh"></i> 重置
                    </button>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <table id="resumeTable" class="table table-striped table-hover">
            <thead>
                <tr>
                    <th width="40">
                        <input type="checkbox" id="checkAll" onchange="toggleCheckAll()">
                    </th>
                    <th width="80">序号</th>
                    <th width="100">姓名</th>
                    <th width="120">手机号</th>
                    <th width="150">邮箱</th>
                    <th width="120">应聘职位</th>
                    <th width="80">状态</th>
                    <th width="140">提交时间</th>
                    <th width="140">审核时间</th>
                    <th width="100">审核人</th>
                    <th width="200">操作</th>
                </tr>
            </thead>
            <tbody id="resumeTableBody">
                <!-- 数据将通过Ajax加载 -->
            </tbody>
        </table>

        <!-- 分页 -->
        <div class="panel-footer">
            <div class="pagination-wrapper">
                <div class="pagination-info">
                    共 <span id="totalCount">0</span> 条记录
                </div>
                <div class="pagination-controls">
                    <button type="button" class="btn btn-sm btn-default" onclick="changePage(1)">首页</button>
                    <button type="button" class="btn btn-sm btn-default" onclick="changePage(currentPage - 1)">上一页</button>
                    <span>第 <span id="currentPageSpan">1</span> 页，共 <span id="totalPageSpan">1</span> 页</span>
                    <button type="button" class="btn btn-sm btn-default" onclick="changePage(currentPage + 1)">下一页</button>
                    <button type="button" class="btn btn-sm btn-default" onclick="changePage(totalPage)">末页</button>
                    <select class="form-control input-sm" id="pageSizeSelect" onchange="changePageSize()">
                        <option value="20">20条/页</option>
                        <option value="50">50条/页</option>
                        <option value="100">100条/页</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- 简历详情模态框 -->
    <div class="modal fade" id="resumeDetailModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">简历详情</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body" id="resumeDetailContent">
                    <!-- 详情内容将通过Ajax加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 审核模态框 -->
    <div class="modal fade" id="reviewModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">简历审核</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="reviewForm">
                        <input type="hidden" id="reviewResumeId">
                        <div class="form-group">
                            <label>审核结果</label>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="reviewStatus" value="1"> 通过
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="reviewStatus" value="2"> 拒绝
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>审核备注</label>
                            <textarea class="form-control" id="reviewRemark" rows="3" placeholder="请填写审核意见..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="submitReview()">确定</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 转换员工模态框 -->
    <div class="modal fade" id="convertModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title">转换为员工档案</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="convertForm">
                        <input type="hidden" id="convertResumeId">
                        <div class="form-group">
                            <label>所属部门</label>
                            <select class="form-control" id="convertDeptId">
                                <option value="">请选择部门</option>
                                <!-- 部门选项将通过Ajax加载 -->
                            </select>
                        </div>
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i>
                            转换后将在员工档案中创建对应记录，简历状态将变更为"已入职"。
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-success" onclick="submitConvert()">确定转换</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        var currentPage = 1;
        var pageSize = 20;
        var totalPage = 1;
        var totalCount = 0;

        // 页面加载完成后初始化
        $(document).ready(function() {
            loadResumeList();
            loadDepartments();
            
            // 绑定搜索框回车事件
            $('#searchKey').keypress(function(e) {
                if (e.which == 13) {
                    searchResume();
                }
            });
        });

        // 加载简历列表
        function loadResumeList() {
            var params = {
                page: currentPage,
                limit: pageSize,
                key: $('#searchKey').val(),
                status: $('#searchStatus').val(),
                position: $('#searchPosition').val(),
                start_date: $('#searchStartDate').val(),
                end_date: $('#searchEndDate').val()
            };

            $.post('?a=data', params, function(response) {
                if (response.total !== undefined) {
                    totalCount = response.total;
                    totalPage = Math.ceil(totalCount / pageSize);
                    
                    updatePagination();
                    renderResumeTable(response.rows || []);
                } else {
                    showMessage('加载数据失败', 'error');
                }
            }, 'json').fail(function() {
                showMessage('网络错误', 'error');
            });
        }

        // 渲染表格
        function renderResumeTable(rows) {
            var html = '';
            var startIndex = (currentPage - 1) * pageSize;
            
            if (rows.length === 0) {
                html = '<tr><td colspan="11" class="text-center">暂无数据</td></tr>';
            } else {
                $.each(rows, function(index, row) {
                    var statusClass = getStatusClass(row.status);
                    var statusText = row.status_text || '未知';
                    
                    html += '<tr>';
                    html += '<td><input type="checkbox" class="row-checkbox" value="' + row.id + '"></td>';
                    html += '<td>' + (startIndex + index + 1) + '</td>';
                    html += '<td>' + (row.name || '') + '</td>';
                    html += '<td>' + (row.mobile || '') + '</td>';
                    html += '<td>' + (row.email || '') + '</td>';
                    html += '<td>' + (row.position || '') + '</td>';
                    html += '<td><span class="label ' + statusClass + '">' + statusText + '</span></td>';
                    html += '<td>' + (row.submit_time || '') + '</td>';
                    html += '<td>' + (row.review_time || '') + '</td>';
                    html += '<td>' + (row.reviewer_name || '') + '</td>';
                    html += '<td>';
                    html += '<button class="btn btn-xs btn-info" onclick="viewDetail(' + row.id + ')">详情</button> ';
                    
                    if (row.status == 0) {
                        html += '<button class="btn btn-xs btn-success" onclick="reviewResume(' + row.id + ')">审核</button> ';
                    }
                    
                    if (row.status == 1) {
                        html += '<button class="btn btn-xs btn-primary" onclick="convertToEmployee(' + row.id + ')">转员工</button> ';
                    }
                    
                    html += '<button class="btn btn-xs btn-danger" onclick="deleteResume(' + row.id + ')">删除</button>';
                    html += '</td>';
                    html += '</tr>';
                });
            }
            
            $('#resumeTableBody').html(html);
        }

        // 获取状态样式类
        function getStatusClass(status) {
            switch (status) {
                case 0: return 'label-warning';
                case 1: return 'label-success';
                case 2: return 'label-danger';
                case 3: return 'label-info';
                default: return 'label-default';
            }
        }

        // 更新分页信息
        function updatePagination() {
            $('#totalCount').text(totalCount);
            $('#currentPageSpan').text(currentPage);
            $('#totalPageSpan').text(totalPage);
        }

        // 搜索
        function searchResume() {
            currentPage = 1;
            loadResumeList();
        }

        // 重置搜索
        function resetSearch() {
            $('#searchKey').val('');
            $('#searchStatus').val('');
            $('#searchPosition').val('');
            $('#searchStartDate').val('');
            $('#searchEndDate').val('');
            currentPage = 1;
            loadResumeList();
        }

        // 刷新列表
        function refreshResumeList() {
            loadResumeList();
        }

        // 切换页面
        function changePage(page) {
            if (page < 1 || page > totalPage) return;
            currentPage = page;
            loadResumeList();
        }

        // 改变页面大小
        function changePageSize() {
            pageSize = parseInt($('#pageSizeSelect').val());
            currentPage = 1;
            loadResumeList();
        }

        // 全选/取消全选
        function toggleCheckAll() {
            var checked = $('#checkAll').prop('checked');
            $('.row-checkbox').prop('checked', checked);
        }

        // 查看详情
        function viewDetail(id) {
            window.open('?a=detail&id=' + id, '_blank');
        }

        // 审核简历
        function reviewResume(id) {
            $('#reviewResumeId').val(id);
            $('#reviewForm')[0].reset();
            $('#reviewModal').modal('show');
        }

        // 提交审核
        function submitReview() {
            var id = $('#reviewResumeId').val();
            var status = $('input[name="reviewStatus"]:checked').val();
            var remark = $('#reviewRemark').val();
            
            if (!status) {
                showMessage('请选择审核结果', 'warning');
                return;
            }
            
            $.post('?a=review', {
                id: id,
                status: status,
                remark: remark
            }, function(response) {
                if (response.success) {
                    showMessage(response.msg, 'success');
                    $('#reviewModal').modal('hide');
                    loadResumeList();
                } else {
                    showMessage(response.msg || '审核失败', 'error');
                }
            }, 'json');
        }

        // 批量审核
        function batchReview(status) {
            var ids = [];
            $('.row-checkbox:checked').each(function() {
                ids.push($(this).val());
            });
            
            if (ids.length === 0) {
                showMessage('请选择要审核的简历', 'warning');
                return;
            }
            
            var statusText = status == 1 ? '通过' : '拒绝';
            if (!confirm('确定要批量' + statusText + '选中的简历吗？')) {
                return;
            }
            
            $.post('?a=batchReview', {
                ids: ids.join(','),
                status: status
            }, function(response) {
                if (response.success) {
                    showMessage(response.msg, 'success');
                    loadResumeList();
                } else {
                    showMessage(response.msg || '批量审核失败', 'error');
                }
            }, 'json');
        }

        // 转换为员工
        function convertToEmployee(id) {
            $('#convertResumeId').val(id);
            $('#convertModal').modal('show');
        }

        // 提交转换
        function submitConvert() {
            var id = $('#convertResumeId').val();
            var deptId = $('#convertDeptId').val();
            
            $.post('?a=convertToEmployee', {
                id: id,
                dept_id: deptId
            }, function(response) {
                if (response.success) {
                    showMessage(response.msg, 'success');
                    $('#convertModal').modal('hide');
                    loadResumeList();
                } else {
                    showMessage(response.msg || '转换失败', 'error');
                }
            }, 'json');
        }

        // 删除简历
        function deleteResume(id) {
            if (!confirm('确定要删除这份简历吗？删除后无法恢复！')) {
                return;
            }
            
            $.post('?a=delete', {id: id}, function(response) {
                if (response.success) {
                    showMessage(response.msg, 'success');
                    loadResumeList();
                } else {
                    showMessage(response.msg || '删除失败', 'error');
                }
            }, 'json');
        }

        // 导出简历
        function exportResume() {
            var params = {
                status: $('#searchStatus').val(),
                start_date: $('#searchStartDate').val(),
                end_date: $('#searchEndDate').val()
            };
            
            var url = '?a=export&' + $.param(params);
            window.open(url);
        }

        // 加载部门列表
        function loadDepartments() {
            // 这里应该调用部门接口，暂时使用静态数据
            var depts = [
                {id: 1, name: '技术部'},
                {id: 2, name: '市场部'},
                {id: 3, name: '人事部'},
                {id: 4, name: '财务部'}
            ];
            
            var html = '<option value="">请选择部门</option>';
            $.each(depts, function(index, dept) {
                html += '<option value="' + dept.id + '">' + dept.name + '</option>';
            });
            $('#convertDeptId').html(html);
        }

        // 显示消息
        function showMessage(message, type) {
            // 这里应该使用系统的消息提示组件
            alert(message);
        }
    </script>
</body>
</html>
