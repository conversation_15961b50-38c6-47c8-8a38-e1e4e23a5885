# 简历管理模块部署检查清单

## 📋 部署前准备

### ✅ 系统环境检查
- [ ] 信呼OA系统版本 ≥ 2.5.0
- [ ] PHP版本 ≥ 7.0
- [ ] MySQL版本 ≥ 5.6
- [ ] 磁盘空间充足（建议预留500MB以上）
- [ ] 网络连接正常

### ✅ 权限检查
- [ ] xinhu/webmain/main/ 目录可写
- [ ] xinhu/web/ 目录可写
- [ ] xinhu/upload/ 目录可写
- [ ] 数据库用户有CREATE、ALTER、INSERT、UPDATE、DELETE权限

### ✅ 备份准备
- [ ] 数据库完整备份
- [ ] 网站文件备份
- [ ] 配置文件备份

## 📦 文件部署清单

### ✅ 核心文件
- [ ] `resumeModel.php` → `xinhu/webmain/model/resumeModel.php`
- [ ] `resumeAction.php` → `xinhu/webmain/main/resume/resumeAction.php`
- [ ] `tpl_resume.html` → `xinhu/webmain/main/resume/tpl_resume.html`
- [ ] `tpl_resume_detail.html` → `xinhu/webmain/main/resume/tpl_resume_detail.html`

### ✅ 外链文件
- [ ] `resume_public_form.html` → `xinhu/web/resume/index.html`
- [ ] `resume_submit.php` → `xinhu/web/resume/submit.php`

### ✅ 工具类文件
- [ ] `resume_upload_handler.php` → `xinhu/webmain/include/ResumeUploadHandler.php`
- [ ] `resume_security.php` → `xinhu/webmain/include/ResumeSecurityManager.php`

### ✅ 安装文件
- [ ] `resume_upgrade.sql` - SQL升级脚本
- [ ] `install_resume_module.php` - 自动安装脚本

### ✅ 文档文件
- [ ] `README_RESUME_MODULE.md` - 使用说明
- [ ] `DEPLOYMENT_CHECKLIST.md` - 部署清单

## 🗄️ 数据库部署

### ✅ 表结构创建
- [ ] `xinhu_resume` - 简历主表
- [ ] `xinhu_resume_detail` - 简历详情表
- [ ] `xinhu_resume_log` - 操作日志表
- [ ] `xinhu_resume_stats` - 统计表（可选）

### ✅ 菜单配置
- [ ] 简历管理主菜单
- [ ] 简历列表子菜单
- [ ] 简历审核子菜单
- [ ] 简历统计子菜单

### ✅ 系统配置
- [ ] 模块版本信息
- [ ] 文件上传配置
- [ ] 安全配置选项

## 🔧 配置部署

### ✅ 文件权限设置
```bash
# 设置目录权限
chmod 755 xinhu/webmain/main/resume/
chmod 755 xinhu/web/resume/
chmod 755 xinhu/upload/resume/

# 设置文件权限
chmod 644 xinhu/webmain/main/resume/*.php
chmod 644 xinhu/webmain/main/resume/*.html
chmod 644 xinhu/web/resume/*
```

### ✅ 上传目录保护
- [ ] 创建 `xinhu/upload/resume/.htaccess` 文件
- [ ] 禁止执行PHP脚本
- [ ] 禁止目录浏览

### ✅ 模块注册
- [ ] 在 `modeModel.php` 中注册模块
- [ ] 配置菜单ID映射
- [ ] 设置权限控制

## 🧪 功能测试

### ✅ 外链功能测试
- [ ] 访问简历填写页面：`http://domain.com/xinhu/web/resume/`
- [ ] 测试表单提交功能
- [ ] 测试文件上传功能
- [ ] 测试数据验证功能
- [ ] 测试错误处理机制

### ✅ 后台管理测试
- [ ] 登录后台查看简历管理菜单
- [ ] 测试简历列表显示
- [ ] 测试搜索和筛选功能
- [ ] 测试简历详情查看
- [ ] 测试审核功能
- [ ] 测试批量操作
- [ ] 测试导出功能

### ✅ 权限测试
- [ ] 测试不同角色的访问权限
- [ ] 测试菜单显示权限
- [ ] 测试操作权限控制

### ✅ 安全测试
- [ ] 测试访问频率限制
- [ ] 测试文件上传安全
- [ ] 测试SQL注入防护
- [ ] 测试XSS攻击防护

## 📊 性能测试

### ✅ 负载测试
- [ ] 测试并发简历提交
- [ ] 测试大量数据查询性能
- [ ] 测试文件上传性能
- [ ] 测试数据库查询优化

### ✅ 容量测试
- [ ] 测试大文件上传
- [ ] 测试大量简历数据处理
- [ ] 测试存储空间使用

## 🔍 部署验证

### ✅ 基础功能验证
```bash
# 检查文件是否存在
ls -la xinhu/webmain/model/resumeModel.php
ls -la xinhu/webmain/main/resume/resumeAction.php
ls -la xinhu/web/resume/index.html

# 检查目录权限
ls -ld xinhu/upload/resume/

# 检查数据表
mysql -u username -p -e "SHOW TABLES LIKE 'xinhu_resume%';" database_name
```

### ✅ 网络访问验证
```bash
# 测试外链访问
curl -I http://domain.com/xinhu/web/resume/

# 测试后台访问
curl -I http://domain.com/xinhu/?m=main&a=resume
```

### ✅ 日志检查
- [ ] 检查PHP错误日志
- [ ] 检查Web服务器访问日志
- [ ] 检查数据库错误日志
- [ ] 检查模块安装日志

## 🚀 上线部署

### ✅ 生产环境配置
- [ ] 关闭调试模式
- [ ] 设置错误日志记录
- [ ] 配置监控告警
- [ ] 设置定时任务（日志清理等）

### ✅ 用户培训
- [ ] HR人员后台操作培训
- [ ] 系统管理员维护培训
- [ ] 用户使用手册分发

### ✅ 监控设置
- [ ] 设置系统监控
- [ ] 配置性能监控
- [ ] 设置错误告警
- [ ] 配置备份策略

## 🔄 部署后维护

### ✅ 定期维护任务
```bash
# 每周执行 - 清理过期日志
php -r "
require_once 'xinhu/webmain/include/ResumeSecurityManager.php';
\$manager = new ResumeSecurityManager();
\$count = \$manager->cleanupLogs(30);
echo 'Cleaned ' . \$count . ' log entries\n';
"

# 每月执行 - 清理过期文件
php -r "
require_once 'xinhu/webmain/include/ResumeUploadHandler.php';
\$handler = new ResumeUploadHandler();
\$count = \$handler->cleanupOldFiles(90);
echo 'Cleaned ' . \$count . ' old files\n';
"
```

### ✅ 监控指标
- [ ] 简历提交成功率
- [ ] 文件上传成功率
- [ ] 系统响应时间
- [ ] 错误发生频率
- [ ] 存储空间使用情况

## 📞 应急处理

### ✅ 常见问题处理
1. **外链无法访问**
   - 检查Web服务器配置
   - 检查文件权限
   - 检查防火墙设置

2. **文件上传失败**
   - 检查PHP上传配置
   - 检查磁盘空间
   - 检查目录权限

3. **数据库连接错误**
   - 检查数据库服务状态
   - 检查连接配置
   - 检查用户权限

4. **菜单不显示**
   - 检查权限配置
   - 检查菜单数据
   - 清除缓存

### ✅ 回滚方案
- [ ] 数据库回滚脚本准备
- [ ] 文件回滚方案准备
- [ ] 回滚测试验证

## ✅ 部署完成确认

- [ ] 所有功能测试通过
- [ ] 性能测试达标
- [ ] 安全测试通过
- [ ] 用户培训完成
- [ ] 监控配置完成
- [ ] 文档交付完成

---

**部署负责人：** _______________  
**部署日期：** _______________  
**验收人员：** _______________  
**验收日期：** _______________  

**备注：** 
- 部署过程中如遇问题，请及时记录并联系技术支持
- 建议在测试环境完整验证后再部署到生产环境
- 部署完成后请保留此清单作为维护参考
