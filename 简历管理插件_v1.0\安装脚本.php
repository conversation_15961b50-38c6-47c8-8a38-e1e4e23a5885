<?php
/**
 * 简历管理插件安装脚本
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */

// 防止直接访问
if (!defined('XINHU_PATH')) {
    define('XINHU_PATH', dirname(dirname(__DIR__)));
}

class ResumePluginInstaller
{
    private $pluginPath;
    private $config;
    private $db;
    private $installLog = [];
    private $xinhuPath;
    
    public function __construct($pluginPath = null)
    {
        $this->pluginPath = $pluginPath ?: dirname(__FILE__);
        $this->xinhuPath = XINHU_PATH;
        $this->loadConfig();
        $this->initDatabase();
    }
    
    /**
     * 加载插件配置
     */
    private function loadConfig()
    {
        $configFile = $this->pluginPath . '/插件配置.json';
        if (!file_exists($configFile)) {
            throw new Exception('插件配置文件不存在: ' . $configFile);
        }
        
        $configContent = file_get_contents($configFile);
        $this->config = json_decode($configContent, true);
        
        if (!$this->config) {
            throw new Exception('插件配置文件格式错误');
        }
    }
    
    /**
     * 初始化数据库连接
     */
    private function initDatabase()
    {
        try {
            // 引入信呼系统核心文件
            require_once $this->xinhuPath . '/webmain/model/base.php';
            global $rock;
            $this->db = $rock->db;
        } catch (Exception $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 执行安装
     */
    public function install()
    {
        try {
            $this->log('开始安装插件: ' . $this->config['display_name']);
            
            // 1. 检查系统要求
            $this->checkRequirements();
            
            // 2. 检查插件是否已安装
            $this->checkIfInstalled();
            
            // 3. 创建备份
            $this->createBackup();
            
            // 4. 创建目录
            $this->createDirectories();
            
            // 5. 复制文件
            $this->copyFiles();
            
            // 6. 执行数据库安装
            $this->installDatabase();
            
            // 7. 创建菜单
            $this->createMenus();
            
            // 8. 设置权限
            $this->setupPermissions();
            
            // 9. 初始化配置
            $this->initializeSettings();
            
            // 10. 执行安装后钩子
            $this->executeHook('after_install');
            
            // 11. 记录安装信息
            $this->recordInstallation();
            
            $this->log('插件安装完成');
            return $this->getResult(true, '安装成功');
            
        } catch (Exception $e) {
            $this->log('安装失败: ' . $e->getMessage(), 'error');
            $this->rollback();
            return $this->getResult(false, $e->getMessage());
        }
    }
    
    /**
     * 检查系统要求
     */
    private function checkRequirements()
    {
        $this->log('检查系统要求...');
        $requirements = $this->config['requirements'];
        
        // 检查信呼版本
        $xinhuVersion = $this->getXinhuVersion();
        if (version_compare($xinhuVersion, ltrim($requirements['xinhu_version'], '>= '), '<')) {
            throw new Exception('需要信呼系统版本 ' . $requirements['xinhu_version']);
        }
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, ltrim($requirements['php_version'], '>= '), '<')) {
            throw new Exception('需要PHP版本 ' . $requirements['php_version']);
        }
        
        // 检查PHP扩展
        foreach ($requirements['extensions'] as $ext) {
            if (!extension_loaded($ext)) {
                throw new Exception('缺少必要的PHP扩展: ' . $ext);
            }
        }
        
        // 检查内存限制
        if (isset($requirements['memory_limit'])) {
            $memoryLimit = ini_get('memory_limit');
            if ($memoryLimit != -1 && $this->parseSize($memoryLimit) < $this->parseSize($requirements['memory_limit'])) {
                throw new Exception('PHP内存限制不足，需要至少: ' . $requirements['memory_limit']);
            }
        }
        
        // 检查目录权限
        $this->checkDirectoryPermissions();
        
        $this->log('系统要求检查通过');
    }
    
    /**
     * 检查插件是否已安装
     */
    private function checkIfInstalled()
    {
        $plugin = $this->db->getone(
            "SELECT * FROM xinhu_plugin WHERE name = '{$this->config['name']}'"
        );
        
        if ($plugin) {
            throw new Exception('插件已经安装，版本: ' . $plugin['version']);
        }
    }
    
    /**
     * 创建备份
     */
    private function createBackup()
    {
        $this->log('创建安装备份...');
        
        $backupDir = $this->xinhuPath . '/upload/backup/plugin_install_' . date('YmdHis') . '/';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        // 备份可能被覆盖的文件
        foreach ($this->config['files']['copy'] as $file) {
            $targetPath = $this->xinhuPath . '/' . $file['to'];
            if (file_exists($targetPath)) {
                $backupPath = $backupDir . str_replace('/', '_', $file['to']);
                copy($targetPath, $backupPath);
                $this->log('备份文件: ' . $file['to']);
            }
        }
        
        $this->log('备份创建完成: ' . $backupDir);
    }
    
    /**
     * 创建目录
     */
    private function createDirectories()
    {
        $this->log('创建目录结构...');
        
        foreach ($this->config['files']['create_dirs'] as $dir) {
            $dirPath = is_array($dir) ? $dir['path'] : $dir;
            $permission = is_array($dir) ? octdec($dir['permission']) : 0755;
            
            $fullPath = $this->xinhuPath . '/' . $dirPath;
            if (!is_dir($fullPath)) {
                if (!mkdir($fullPath, $permission, true)) {
                    throw new Exception('无法创建目录: ' . $dirPath);
                }
                $this->log('创建目录: ' . $dirPath);
            }
        }
    }
    
    /**
     * 复制文件
     */
    private function copyFiles()
    {
        $this->log('复制插件文件...');
        
        foreach ($this->config['files']['copy'] as $file) {
            $sourcePath = $this->pluginPath . '/' . $file['from'];
            $targetPath = $this->xinhuPath . '/' . $file['to'];
            
            if (is_dir($sourcePath)) {
                $this->copyDirectory($sourcePath, $targetPath);
            } else {
                if (!file_exists($sourcePath)) {
                    throw new Exception('源文件不存在: ' . $sourcePath);
                }
                
                $targetDir = dirname($targetPath);
                if (!is_dir($targetDir)) {
                    mkdir($targetDir, 0755, true);
                }
                
                if (!copy($sourcePath, $targetPath)) {
                    throw new Exception('无法复制文件: ' . $file['from'] . ' -> ' . $file['to']);
                }
                
                $this->log('复制文件: ' . $file['to']);
            }
        }
    }
    
    /**
     * 递归复制目录
     */
    private function copyDirectory($source, $target)
    {
        if (!is_dir($target)) {
            mkdir($target, 0755, true);
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $targetPath = $target . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!is_dir($targetPath)) {
                    mkdir($targetPath, 0755, true);
                }
            } else {
                copy($item, $targetPath);
            }
        }
        
        $this->log('复制目录: ' . basename($source));
    }
    
    /**
     * 安装数据库
     */
    private function installDatabase()
    {
        $this->log('执行数据库安装...');
        
        $sqlFile = $this->pluginPath . '/sql/安装脚本.sql';
        if (file_exists($sqlFile)) {
            $this->executeSqlFile($sqlFile);
        }
        
        $this->log('数据库安装完成');
    }
    
    /**
     * 执行SQL文件
     */
    private function executeSqlFile($sqlFile)
    {
        $sqlContent = file_get_contents($sqlFile);
        $sqlStatements = explode('ROCKSPLIT', $sqlContent);
        
        foreach ($sqlStatements as $sql) {
            $sql = trim($sql);
            if (empty($sql) || strpos($sql, '--') === 0) {
                continue;
            }
            
            // 替换表前缀
            $sql = str_replace('[Q]', 'xinhu_', $sql);
            
            try {
                $this->db->query($sql);
                $this->log('执行SQL: ' . substr($sql, 0, 50) . '...');
            } catch (Exception $e) {
                // 某些SQL可能因为已存在而失败，记录警告但继续
                $this->log('SQL执行警告: ' . $e->getMessage(), 'warning');
            }
        }
    }
    
    /**
     * 创建菜单
     */
    private function createMenus()
    {
        $this->log('创建菜单结构...');
        
        foreach ($this->config['menus'] as $menu) {
            $this->createMenu($menu);
        }
    }
    
    /**
     * 创建单个菜单
     */
    private function createMenu($menuConfig, $parentId = null)
    {
        // 查找父级菜单ID
        if (!$parentId && isset($menuConfig['parent_name'])) {
            $parent = $this->db->getone(
                "SELECT id FROM xinhu_menu WHERE name = '{$menuConfig['parent_name']}'"
            );
            $parentId = $parent ? $parent['id'] : 0;
        }
        
        // 检查菜单是否已存在
        $existing = $this->db->getone(
            "SELECT id FROM xinhu_menu WHERE name = '{$menuConfig['name']}'"
        );
        
        if (!$existing) {
            $menuData = [
                'name' => $menuConfig['name'],
                'pid' => $parentId ?: 0,
                'sort' => $menuConfig['sort'],
                'url' => $menuConfig['url'],
                'icons' => $menuConfig['icon'],
                'ispir' => 1,
                'status' => 1,
                'type' => 0
            ];
            
            $menuId = $this->db->insert('xinhu_menu', $menuData);
            $this->log('创建菜单: ' . $menuConfig['name']);
            
            // 创建子菜单
            if (isset($menuConfig['children'])) {
                foreach ($menuConfig['children'] as $child) {
                    $this->createMenu($child, $menuId);
                }
            }
        }
    }
    
    /**
     * 设置权限
     */
    private function setupPermissions()
    {
        $this->log('设置权限配置...');
        // 这里可以根据需要设置默认权限
        // 实际权限分配通常在后台管理中进行
    }
    
    /**
     * 初始化设置
     */
    private function initializeSettings()
    {
        $this->log('初始化插件设置...');
        
        foreach ($this->config['settings'] as $category => $settings) {
            foreach ($settings as $key => $value) {
                $optionName = "resume_{$category}_{$key}";
                $optionValue = is_array($value) ? json_encode($value) : $value;
                
                $this->db->insert('xinhu_option', [
                    'name' => $optionName,
                    'value' => $optionValue,
                    'memo' => "简历管理插件设置: {$category}.{$key}"
                ]);
            }
        }
    }
    
    /**
     * 执行钩子
     */
    private function executeHook($hookName)
    {
        if (isset($this->config['hooks'][$hookName])) {
            $method = $this->config['hooks'][$hookName];
            if (method_exists($this, $method)) {
                $this->$method();
            }
        }
    }
    
    /**
     * 记录安装信息
     */
    private function recordInstallation()
    {
        $installData = [
            'name' => $this->config['name'],
            'display_name' => $this->config['display_name'],
            'version' => $this->config['version'],
            'status' => 1,
            'install_time' => date('Y-m-d H:i:s'),
            'config' => json_encode($this->config),
            'install_log' => json_encode($this->installLog)
        ];
        
        $this->db->insert('xinhu_plugin', $installData);
    }
    
    /**
     * 回滚安装
     */
    private function rollback()
    {
        $this->log('开始回滚安装...', 'warning');
        
        try {
            // 删除创建的文件
            foreach ($this->config['files']['copy'] as $file) {
                $targetPath = $this->xinhuPath . '/' . $file['to'];
                if (file_exists($targetPath)) {
                    if (is_dir($targetPath)) {
                        $this->removeDirectory($targetPath);
                    } else {
                        unlink($targetPath);
                    }
                }
            }
            
            // 删除数据表
            if (isset($this->config['database']['tables'])) {
                foreach ($this->config['database']['tables'] as $table) {
                    $tableName = is_array($table) ? $table['name'] : $table;
                    $this->db->query("DROP TABLE IF EXISTS {$tableName}");
                }
            }
            
            // 删除菜单
            foreach ($this->config['menus'] as $menu) {
                $this->db->delete('xinhu_menu', "name = '{$menu['name']}'");
            }
            
            $this->log('回滚完成');
            
        } catch (Exception $e) {
            $this->log('回滚失败: ' . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 获取信呼版本
     */
    private function getXinhuVersion()
    {
        $version = $this->db->getone("SELECT value FROM xinhu_option WHERE name = 'version'");
        return $version ? $version['value'] : '0.0.0';
    }
    
    /**
     * 检查目录权限
     */
    private function checkDirectoryPermissions()
    {
        $checkDirs = ['webmain/main', 'web', 'upload'];
        foreach ($checkDirs as $dir) {
            $fullPath = $this->xinhuPath . '/' . $dir;
            if (!is_writable($fullPath)) {
                throw new Exception('目录不可写: ' . $dir);
            }
        }
    }
    
    /**
     * 解析大小字符串
     */
    private function parseSize($size)
    {
        $unit = preg_replace('/[^bkmgtpezy]/i', '', $size);
        $size = preg_replace('/[^0-9\.]/', '', $size);
        
        if ($unit) {
            return round($size * pow(1024, stripos('bkmgtpezy', $unit[0])));
        }
        return round($size);
    }
    
    /**
     * 递归删除目录
     */
    private function removeDirectory($dir)
    {
        if (is_dir($dir)) {
            $objects = scandir($dir);
            foreach ($objects as $object) {
                if ($object != "." && $object != "..") {
                    if (is_dir($dir . "/" . $object)) {
                        $this->removeDirectory($dir . "/" . $object);
                    } else {
                        unlink($dir . "/" . $object);
                    }
                }
            }
            rmdir($dir);
        }
    }
    
    /**
     * 添加日志
     */
    private function log($message, $level = 'info')
    {
        $this->installLog[] = [
            'time' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message
        ];
    }
    
    /**
     * 获取结果
     */
    private function getResult($success, $message)
    {
        return [
            'success' => $success,
            'message' => $message,
            'plugin' => $this->config['display_name'],
            'version' => $this->config['version'],
            'log' => $this->installLog
        ];
    }
    
    /**
     * 安装后钩子
     */
    private function afterInstall()
    {
        $this->log('执行安装后处理...');
        
        // 创建上传目录的保护文件
        $uploadDir = $this->xinhuPath . '/upload/resume/';
        if (is_dir($uploadDir)) {
            // 创建.htaccess文件
            $htaccessContent = "Options -Indexes\n";
            $htaccessContent .= "Order allow,deny\n";
            $htaccessContent .= "Allow from all\n";
            $htaccessContent .= "<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n";
            $htaccessContent .= "    deny from all\n";
            $htaccessContent .= "</Files>\n";
            
            file_put_contents($uploadDir . '.htaccess', $htaccessContent);
            
            // 创建index.html防止目录浏览
            file_put_contents($uploadDir . 'index.html', '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>');
        }
    }
}

// 如果直接访问此文件，执行安装
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    try {
        $installer = new ResumePluginInstaller();
        $result = $installer->install();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        header('Content-Type: application/json; charset=utf-8');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'plugin' => '简历管理插件',
            'version' => '1.0.0'
        ], JSON_UNESCAPED_UNICODE);
    }
}
