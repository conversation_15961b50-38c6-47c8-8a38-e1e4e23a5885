# 信呼系统简历管理模块设计文档

## 📋 项目概述

### 目标
为信呼OA系统添加简历管理功能，包括：
1. 外链简历填写页面（公开访问）
2. 后台简历管理系统（HR使用）
3. 与现有人事档案系统集成

### 技术方案
采用升级包方式集成，确保与官方更新兼容，最小化冲突风险。

## 🏗️ 系统架构设计

### 1. 模块结构
```
resume_module/
├── files/                          # 文件目录
│   ├── webmain/main/resume/        # 后台管理模块
│   │   ├── resumeAction.php        # 控制器
│   │   ├── tpl_resume.html         # 列表页面
│   │   ├── tpl_resume_form.html    # 详情页面
│   │   └── rock_resume.php         # 页面脚本
│   ├── webmain/model/              # 数据模型
│   │   └── resumeModel.php         # 简历数据模型
│   ├── web/resume/                 # 外链访问
│   │   ├── index.php               # 简历填写页面
│   │   ├── submit.php              # 提交处理
│   │   ├── upload.php              # 文件上传
│   │   └── assets/                 # 静态资源
│   └── upload/data/                # 升级脚本
│       └── resume_upgrade.txt      # SQL升级脚本
├── install.php                     # 安装脚本
├── uninstall.php                   # 卸载脚本
└── README.md                       # 说明文档
```

### 2. 数据库设计

#### 主表：xinhu_resume
```sql
CREATE TABLE `xinhu_resume` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `mobile` varchar(20) NOT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `position` varchar(50) DEFAULT NULL COMMENT '应聘职位',
  `resume_file` varchar(200) DEFAULT NULL COMMENT '简历文件路径',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态:0待审核,1通过,2拒绝,3已入职',
  `submit_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `reviewer_id` int(11) DEFAULT '0' COMMENT '审核人ID',
  `reviewer_name` varchar(50) DEFAULT NULL COMMENT '审核人姓名',
  `remark` text COMMENT '备注',
  `ip_address` varchar(45) DEFAULT NULL COMMENT '提交IP',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '浏览器信息',
  PRIMARY KEY (`id`),
  KEY `mobile` (`mobile`),
  KEY `status` (`status`),
  KEY `submit_time` (`submit_time`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='简历管理主表';
```

#### 详情表：xinhu_resume_detail
```sql
CREATE TABLE `xinhu_resume_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `resume_id` int(11) NOT NULL COMMENT '简历ID',
  `gender` tinyint(4) DEFAULT NULL COMMENT '性别:1男,2女',
  `birth_date` date DEFAULT NULL COMMENT '出生日期',
  `education` varchar(20) DEFAULT NULL COMMENT '学历',
  `experience_years` tinyint(4) DEFAULT NULL COMMENT '工作年限',
  `current_salary` decimal(10,2) DEFAULT NULL COMMENT '当前薪资',
  `expected_salary` decimal(10,2) DEFAULT NULL COMMENT '期望薪资',
  `work_experience` text COMMENT '工作经历',
  `education_background` text COMMENT '教育背景',
  `skills` text COMMENT '技能特长',
  `self_introduction` text COMMENT '自我介绍',
  `emergency_contact` varchar(100) DEFAULT NULL COMMENT '紧急联系人',
  `emergency_phone` varchar(20) DEFAULT NULL COMMENT '紧急联系电话',
  PRIMARY KEY (`id`),
  UNIQUE KEY `resume_id` (`resume_id`)
) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='简历详细信息表';
```

### 3. 菜单集成方案

#### 菜单结构
根据现有系统分析，人事档案相关菜单ID：
- userinfo: 85,101,149
- userract: 95
- hrdemand: 265
- hrmanshi: 266

#### 菜单插入策略
```sql
-- 查找人事档案父级菜单（通过userinfo模块反推）
INSERT INTO `[Q]menu` (`name`,`pid`,`sort`,`url`,`icons`,`ispir`) 
SELECT '简历管理',
       (SELECT `pid` FROM `[Q]menu` WHERE `url` LIKE '%userinfo%' LIMIT 1),
       999,
       'main,resume',
       'file-text-o',
       '1'
FROM `[Q]menu` WHERE `id`=1 
AND NOT EXISTS(SELECT 1 FROM `[Q]menu` WHERE `name`='简历管理');
```

### 4. 权限设计

#### 访问控制
- 外链页面：公开访问，IP限制，频率控制
- 后台管理：需要登录，基于角色权限
- 文件访问：权限验证，防盗链

#### 安全措施
1. 文件上传限制（类型、大小）
2. 数据验证和过滤
3. SQL注入防护
4. XSS攻击防护
5. CSRF令牌验证

## 🔧 技术实现要点

### 1. 外链访问设计
```php
// 访问路径：http://domain.com/xinhu/web/resume/
// 特点：
// - 独立于主系统登录
// - 简洁的URL结构
// - 移动端友好
// - 安全防护
```

### 2. 文件上传管理
```php
// 存储路径：xinhu/upload/resume/YYYY-MM/
// 文件命名：timestamp_randomstring.ext
// 支持格式：PDF, DOC, DOCX
// 大小限制：5MB
```

### 3. 数据同步机制
```php
// 简历转员工档案功能
// 1. 验证手机号唯一性
// 2. 创建admin表记录
// 3. 创建userinfo表记录
// 4. 更新简历状态为"已入职"
```

## 📊 开发计划

### 阶段一：基础架构（1-2天）
- [x] 项目规划和架构设计
- [ ] 数据库设计和SQL脚本
- [ ] 后端模型层开发

### 阶段二：核心功能（2-3天）
- [ ] 后端控制器开发
- [ ] 外链简历填写页面
- [ ] 文件上传和管理功能

### 阶段三：界面完善（1-2天）
- [ ] 后台管理界面开发
- [ ] 权限和安全控制

### 阶段四：集成测试（1-2天）
- [ ] 升级包制作和测试
- [ ] 文档编写和部署指南

## 🎯 成功标准

### 功能完整性
- ✅ 外链简历填写正常
- ✅ 文件上传功能正常
- ✅ 后台管理功能完整
- ✅ 权限控制有效

### 系统兼容性
- ✅ 不影响现有功能
- ✅ 升级包安装成功
- ✅ 与官方更新兼容

### 安全性
- ✅ 数据验证完整
- ✅ 文件上传安全
- ✅ 访问权限正确

---

**开发日志记录开始时间：** 2025-06-21
**当前阶段：** 项目规划和架构设计 ✅
**下一步：** 数据库设计和SQL脚本编写
