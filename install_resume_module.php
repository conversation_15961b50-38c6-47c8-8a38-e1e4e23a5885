<?php
/**
 * 简历管理模块安装脚本
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */

class ResumeModuleInstaller
{
    private $db;
    private $config;
    private $installLog = [];
    
    public function __construct()
    {
        $this->initConfig();
        $this->initDatabase();
    }
    
    /**
     * 初始化配置
     */
    private function initConfig()
    {
        $this->config = [
            'module_name' => '简历管理模块',
            'module_version' => '1.0',
            'min_xinhu_version' => '2.5.0',
            'files' => [
                // 后端文件
                'webmain/model/resumeModel.php' => 'resumeModel.php',
                'webmain/main/resume/resumeAction.php' => 'resumeAction.php',
                'webmain/main/resume/tpl_resume.html' => 'tpl_resume.html',
                'webmain/main/resume/tpl_resume_detail.html' => 'tpl_resume_detail.html',
                
                // 外链文件
                'web/resume/index.html' => 'resume_public_form.html',
                'web/resume/submit.php' => 'resume_submit.php',
                
                // 工具类文件
                'webmain/include/ResumeUploadHandler.php' => 'resume_upload_handler.php',
                'webmain/include/ResumeSecurityManager.php' => 'resume_security.php',
            ],
            'directories' => [
                'webmain/main/resume',
                'web/resume',
                'upload/resume'
            ]
        ];
    }
    
    /**
     * 初始化数据库连接
     */
    private function initDatabase()
    {
        try {
            // 这里应该使用信呼系统的数据库连接
            global $rock;
            $this->db = $rock->db;
        } catch (Exception $e) {
            $this->addLog('error', '数据库连接失败: ' . $e->getMessage());
            throw new Exception('数据库连接失败');
        }
    }
    
    /**
     * 执行安装
     * 
     * @return array 安装结果
     */
    public function install()
    {
        try {
            $this->addLog('info', '开始安装简历管理模块...');
            
            // 1. 检查系统兼容性
            $this->checkCompatibility();
            
            // 2. 创建目录
            $this->createDirectories();
            
            // 3. 复制文件
            $this->copyFiles();
            
            // 4. 执行数据库升级
            $this->executeDatabaseUpgrade();
            
            // 5. 设置权限
            $this->setPermissions();
            
            // 6. 验证安装
            $this->verifyInstallation();
            
            // 7. 记录安装信息
            $this->recordInstallation();
            
            $this->addLog('success', '简历管理模块安装完成！');
            
            return [
                'success' => true,
                'message' => '安装成功',
                'log' => $this->installLog
            ];
            
        } catch (Exception $e) {
            $this->addLog('error', '安装失败: ' . $e->getMessage());
            
            // 尝试回滚
            $this->rollback();
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'log' => $this->installLog
            ];
        }
    }
    
    /**
     * 检查系统兼容性
     */
    private function checkCompatibility()
    {
        $this->addLog('info', '检查系统兼容性...');
        
        // 检查信呼系统版本
        $version = $this->db->getone("SELECT `value` FROM xinhu_option WHERE `name`='version'");
        if (!$version || version_compare($version['value'], $this->config['min_xinhu_version'], '<')) {
            throw new Exception('需要信呼系统版本 ' . $this->config['min_xinhu_version'] . ' 或更高');
        }
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '7.0.0', '<')) {
            throw new Exception('需要PHP版本7.0或更高');
        }
        
        // 检查必要的PHP扩展
        $requiredExtensions = ['pdo', 'gd', 'fileinfo'];
        foreach ($requiredExtensions as $ext) {
            if (!extension_loaded($ext)) {
                throw new Exception('缺少必要的PHP扩展: ' . $ext);
            }
        }
        
        // 检查目录权限
        $writableDirs = ['webmain/main', 'web', 'upload'];
        foreach ($writableDirs as $dir) {
            if (!is_writable($dir)) {
                throw new Exception('目录不可写: ' . $dir);
            }
        }
        
        $this->addLog('success', '系统兼容性检查通过');
    }
    
    /**
     * 创建目录
     */
    private function createDirectories()
    {
        $this->addLog('info', '创建目录结构...');
        
        foreach ($this->config['directories'] as $dir) {
            if (!is_dir($dir)) {
                if (!mkdir($dir, 0755, true)) {
                    throw new Exception('无法创建目录: ' . $dir);
                }
                $this->addLog('info', '创建目录: ' . $dir);
            }
        }
        
        $this->addLog('success', '目录结构创建完成');
    }
    
    /**
     * 复制文件
     */
    private function copyFiles()
    {
        $this->addLog('info', '复制模块文件...');
        
        foreach ($this->config['files'] as $target => $source) {
            if (!file_exists($source)) {
                throw new Exception('源文件不存在: ' . $source);
            }
            
            $targetDir = dirname($target);
            if (!is_dir($targetDir)) {
                mkdir($targetDir, 0755, true);
            }
            
            if (!copy($source, $target)) {
                throw new Exception('无法复制文件: ' . $source . ' -> ' . $target);
            }
            
            $this->addLog('info', '复制文件: ' . $target);
        }
        
        $this->addLog('success', '文件复制完成');
    }
    
    /**
     * 执行数据库升级
     */
    private function executeDatabaseUpgrade()
    {
        $this->addLog('info', '执行数据库升级...');
        
        $sqlFile = 'resume_upgrade.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception('SQL升级文件不存在: ' . $sqlFile);
        }
        
        $sqlContent = file_get_contents($sqlFile);
        $sqlStatements = explode('ROCKSPLIT', $sqlContent);
        
        foreach ($sqlStatements as $sql) {
            $sql = trim($sql);
            if (empty($sql) || strpos($sql, '--') === 0) {
                continue;
            }
            
            // 替换表前缀
            $sql = str_replace('[Q]', 'xinhu_', $sql);
            
            try {
                $this->db->query($sql);
                $this->addLog('info', '执行SQL: ' . substr($sql, 0, 100) . '...');
            } catch (Exception $e) {
                // 某些SQL可能因为已存在而失败，这是正常的
                $this->addLog('warning', 'SQL执行警告: ' . $e->getMessage());
            }
        }
        
        $this->addLog('success', '数据库升级完成');
    }
    
    /**
     * 设置权限
     */
    private function setPermissions()
    {
        $this->addLog('info', '设置文件权限...');
        
        // 设置上传目录权限
        $uploadDir = 'upload/resume';
        if (is_dir($uploadDir)) {
            chmod($uploadDir, 0755);
            
            // 创建.htaccess文件
            $htaccessContent = "Options -Indexes\n";
            $htaccessContent .= "Order allow,deny\n";
            $htaccessContent .= "Allow from all\n";
            $htaccessContent .= "<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n";
            $htaccessContent .= "    deny from all\n";
            $htaccessContent .= "</Files>\n";
            
            file_put_contents($uploadDir . '/.htaccess', $htaccessContent);
        }
        
        $this->addLog('success', '权限设置完成');
    }
    
    /**
     * 验证安装
     */
    private function verifyInstallation()
    {
        $this->addLog('info', '验证安装结果...');
        
        // 检查数据表是否创建成功
        $tables = ['xinhu_resume', 'xinhu_resume_detail', 'xinhu_resume_log'];
        foreach ($tables as $table) {
            $result = $this->db->query("SHOW TABLES LIKE '{$table}'");
            if (!$result || $this->db->num_rows($result) == 0) {
                throw new Exception('数据表创建失败: ' . $table);
            }
        }
        
        // 检查菜单是否创建成功
        $menu = $this->db->getone("SELECT id FROM xinhu_menu WHERE name='简历管理'");
        if (!$menu) {
            throw new Exception('菜单创建失败');
        }
        
        // 检查关键文件是否存在
        $keyFiles = [
            'webmain/model/resumeModel.php',
            'webmain/main/resume/resumeAction.php',
            'web/resume/index.html'
        ];
        
        foreach ($keyFiles as $file) {
            if (!file_exists($file)) {
                throw new Exception('关键文件缺失: ' . $file);
            }
        }
        
        $this->addLog('success', '安装验证通过');
    }
    
    /**
     * 记录安装信息
     */
    private function recordInstallation()
    {
        $installData = [
            'module_name' => $this->config['module_name'],
            'module_version' => $this->config['module_version'],
            'install_time' => date('Y-m-d H:i:s'),
            'install_log' => json_encode($this->installLog)
        ];
        
        // 记录到系统选项表
        $this->db->insert('xinhu_option', [
            'name' => 'resume_module_install',
            'value' => json_encode($installData),
            'memo' => '简历管理模块安装信息'
        ]);
    }
    
    /**
     * 回滚安装
     */
    private function rollback()
    {
        $this->addLog('info', '开始回滚安装...');
        
        try {
            // 删除创建的文件
            foreach ($this->config['files'] as $target => $source) {
                if (file_exists($target)) {
                    unlink($target);
                }
            }
            
            // 删除创建的目录（如果为空）
            foreach (array_reverse($this->config['directories']) as $dir) {
                if (is_dir($dir) && count(scandir($dir)) == 2) {
                    rmdir($dir);
                }
            }
            
            // 删除数据表
            $tables = ['xinhu_resume_log', 'xinhu_resume_detail', 'xinhu_resume'];
            foreach ($tables as $table) {
                $this->db->query("DROP TABLE IF EXISTS {$table}");
            }
            
            // 删除菜单
            $this->db->delete('xinhu_menu', "name IN ('简历管理', '简历列表', '简历审核', '简历统计')");
            
            $this->addLog('info', '回滚完成');
            
        } catch (Exception $e) {
            $this->addLog('error', '回滚失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 卸载模块
     * 
     * @return array 卸载结果
     */
    public function uninstall()
    {
        try {
            $this->addLog('info', '开始卸载简历管理模块...');
            
            // 备份数据（可选）
            $this->backupData();
            
            // 执行回滚
            $this->rollback();
            
            // 清理配置
            $this->db->delete('xinhu_option', "name LIKE 'resume_%'");
            
            $this->addLog('success', '简历管理模块卸载完成！');
            
            return [
                'success' => true,
                'message' => '卸载成功',
                'log' => $this->installLog
            ];
            
        } catch (Exception $e) {
            $this->addLog('error', '卸载失败: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'log' => $this->installLog
            ];
        }
    }
    
    /**
     * 备份数据
     */
    private function backupData()
    {
        $backupDir = 'upload/backup/resume_' . date('YmdHis') . '/';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        // 导出数据表
        $tables = ['xinhu_resume', 'xinhu_resume_detail', 'xinhu_resume_log'];
        foreach ($tables as $table) {
            $data = $this->db->getall("SELECT * FROM {$table}");
            file_put_contents($backupDir . $table . '.json', json_encode($data, JSON_UNESCAPED_UNICODE));
        }
        
        $this->addLog('info', '数据备份完成: ' . $backupDir);
    }
    
    /**
     * 添加日志
     * 
     * @param string $level 日志级别
     * @param string $message 日志消息
     */
    private function addLog($level, $message)
    {
        $this->installLog[] = [
            'time' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message
        ];
    }
    
    /**
     * 获取安装日志
     * 
     * @return array
     */
    public function getInstallLog()
    {
        return $this->installLog;
    }
}

// 如果直接访问此文件，执行安装
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    try {
        $installer = new ResumeModuleInstaller();
        $result = $installer->install();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
    }
}
