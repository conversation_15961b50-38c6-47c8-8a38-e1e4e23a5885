# 信呼系统简历管理插件

## 📋 插件简介

简历管理插件是为信呼OA系统开发的标准化人力资源管理扩展插件，提供完整的在线简历收集、管理和审核功能。

### 主要功能

- ✅ **外链简历填写** - 公开访问的简历提交页面
- ✅ **后台管理系统** - 完整的简历管理界面
- ✅ **审核流程** - 简历审核和状态管理
- ✅ **文件上传** - 安全的简历文件上传功能
- ✅ **数据统计** - 简历提交和审核统计
- ✅ **员工转换** - 简历转换为员工档案
- ✅ **权限控制** - 完善的安全和权限管理

## 📦 插件结构

```
简历管理插件_v1.0/
├── 插件配置.json                    # 插件配置文件
├── 安装脚本.php                     # 安装脚本
├── 卸载脚本.php                     # 卸载脚本
├── 插件说明.md                      # 插件说明
├── 更新日志.md                      # 更新日志
├── 许可证.txt                       # 许可证文件
├── 
├── sql/                            # 数据库脚本目录
│   └── 安装脚本.sql                 # 数据库安装脚本
├── 
├── files/                          # 插件文件目录
│   ├── webmain/                    # 后端文件
│   ├── web/resume/                 # 外链文件
│   └── upload/resume/              # 上传目录
├── 
├── docs/                           # 文档目录
├── config/                         # 配置文件目录
└── screenshots/                    # 截图目录
```

## 🚀 安装方法

### 系统要求

**基本要求:**
- 信呼OA系统 2.5.0 或更高版本
- PHP 7.0 或更高版本
- MySQL 5.6 或更高版本
- 支持文件上传功能

**Windows环境支持:**
- ✅ Windows Server 2012/2016/2019/2022
- ✅ Windows 10/11（开发测试环境）
- ✅ Apache for Windows 2.4+
- ✅ 自动路径分隔符处理
- ✅ Windows文件权限管理

### 标准插件安装（推荐）

#### 方法一：通过插件管理器安装

1. **上传插件包**
   ```bash
   # Linux/Unix环境
   unzip 简历管理插件_v1.0.zip -d xinhu/plugins/
   ```
   ```cmd
   # Windows环境
   7z x 简历管理插件_v1.0.zip -o"xinhu\plugins\"
   ```

2. **后台安装**
   - 登录信呼系统后台
   - 访问：系统管理 → 插件管理 → 安装插件
   - 选择"简历管理插件"
   - 点击"安装"按钮
   - 等待安装完成

3. **启用插件**
   - 安装完成后，在插件列表中启用插件
   - 刷新页面，查看"人事档案"菜单下的"简历管理"

#### 方法二：直接执行安装脚本

1. **上传插件包**
   ```bash
   # 上传到插件目录
   xinhu/plugins/resume_manager/
   ```

2. **执行安装**
   ```bash
   # 访问安装脚本
   http://your-domain.com/xinhu/plugins/resume_manager/安装脚本.php
   ```

3. **查看安装结果**
   - 安装脚本会返回JSON格式的安装结果
   - 检查安装日志确认所有步骤完成

### 传统方式安装（兼容）

如果系统不支持插件管理器，可以使用传统方式：

1. **手动复制文件**
   ```
   files/webmain/model/resumeModel.php → xinhu/webmain/model/
   files/webmain/main/resume/ → xinhu/webmain/main/resume/
   files/web/resume/ → xinhu/web/resume/
   ```

2. **执行SQL脚本**
   - 在数据库中执行 `sql/安装脚本.sql` 文件
   - 或通过"系统管理 → 系统升级"执行

3. **配置权限**
   - 在"系统管理 → 权限管理"中分配权限

## 🎯 功能特性

### 外链简历填写

- **公开访问地址**: `http://your-domain.com/xinhu/web/resume/`
- **响应式设计**: 支持PC和移动端访问
- **表单验证**: 完整的前端和后端验证
- **文件上传**: 支持简历附件上传
- **安全防护**: 防止恶意提交和攻击

### 后台管理功能

- **简历列表**: 分页显示所有提交的简历
- **详情查看**: 查看完整的简历信息和附件
- **状态管理**: 待审核、已通过、已拒绝等状态
- **批量操作**: 批量审核、删除、导出
- **搜索筛选**: 按姓名、职位、状态等条件筛选

### 数据统计

- **提交统计**: 按时间段统计简历提交数量
- **状态分布**: 各种审核状态的数量分布
- **职位热度**: 各职位的申请人数统计
- **图表展示**: 直观的数据可视化

### 安全特性

- **访问频率限制**: 防止恶意刷屏
- **文件类型检查**: 限制上传文件类型
- **文件大小限制**: 控制上传文件大小
- **SQL注入防护**: 参数化查询
- **XSS攻击防护**: 输出转义处理

## ⚙️ 配置说明

### 上传设置

```json
{
    "upload": {
        "max_file_size": "10MB",
        "allowed_types": ["pdf", "doc", "docx", "jpg", "png"],
        "upload_path": "upload/resume/",
        "windows_path": "upload\\resume\\"
    }
}
```

### 安全设置

```json
{
    "security": {
        "rate_limit": 10,
        "rate_window": 3600,
        "enable_captcha": true
    }
}
```

### 通知设置

```json
{
    "notification": {
        "email_admin": true,
        "email_hr": true
    }
}
```

## 🔧 使用说明

### 1. 外链简历填写

访问地址：`http://your-domain.com/xinhu/web/resume/`

用户可以在此页面：
- 填写个人基本信息
- 上传简历附件
- 选择应聘职位
- 提交简历申请

### 2. 后台管理

登录后台，在"人事档案"菜单下找到"简历管理"：

- **简历列表**: 查看所有提交的简历
- **简历审核**: 审核简历状态
- **简历统计**: 查看统计数据

### 3. 权限管理

在"系统管理 → 权限管理"中为相关角色分配权限：

- `resume_view`: 查看简历
- `resume_edit`: 编辑简历
- `resume_delete`: 删除简历
- `resume_audit`: 审核简历
- `resume_export`: 导出简历
- `resume_stats`: 查看统计

## 🔄 升级说明

### 版本升级

1. **备份数据**: 升级前请备份数据库和文件
2. **下载新版本**: 获取最新版本的插件包
3. **执行升级**: 通过插件管理器或手动升级
4. **测试功能**: 升级后测试各项功能

### 数据迁移

插件支持从旧版本平滑升级，会自动：
- 保留现有数据
- 更新数据库结构
- 迁移配置设置

## 🛠️ 故障排除

### 常见问题

1. **安装失败**
   - 检查系统要求是否满足
   - 确认目录权限是否正确
   - 查看安装日志获取详细错误信息

2. **文件上传失败**
   - 检查upload目录权限
   - 确认PHP上传限制设置
   - 检查文件类型和大小限制

3. **外链页面无法访问**
   - 检查web目录权限
   - 确认URL重写规则
   - 检查防火墙设置

### 日志查看

- **安装日志**: 插件安装过程的详细日志
- **错误日志**: 系统错误和异常记录
- **访问日志**: 外链页面的访问记录

## 📞 技术支持

如果在使用过程中遇到问题：

1. **查看文档**: 详细阅读使用说明和故障排除
2. **检查日志**: 查看相关日志文件
3. **联系支持**: 提供详细的错误信息和环境描述

**技术支持邮箱**: <EMAIL>  
**官方网站**: https://www.rockoa.com  
**文档地址**: https://docs.rockoa.com

---

**插件版本**: 1.0.0  
**发布日期**: 2025-06-21  
**兼容版本**: 信呼OA 2.5.0+  
**开发团队**: 信呼开发团队
