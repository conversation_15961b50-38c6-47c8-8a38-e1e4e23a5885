<?php
/**
 * 创建插件目录结构
 */

$pluginDir = '简历管理插件_v1.0';

// 创建主目录
if (!is_dir($pluginDir)) {
    mkdir($pluginDir, 0755);
    echo "创建主目录: $pluginDir\n";
}

// 创建子目录
$dirs = [
    'sql',
    'files',
    'files/webmain',
    'files/webmain/model',
    'files/webmain/main',
    'files/webmain/main/resume',
    'files/webmain/include',
    'files/web',
    'files/web/resume',
    'files/web/resume/assets',
    'files/web/resume/assets/css',
    'files/web/resume/assets/js',
    'files/web/resume/assets/images',
    'files/upload',
    'files/upload/resume',
    'docs',
    'config',
    'tests',
    'tests/unit',
    'tests/integration',
    'tests/fixtures',
    'screenshots',
    'legacy'
];

foreach ($dirs as $dir) {
    $fullPath = $pluginDir . '/' . $dir;
    if (!is_dir($fullPath)) {
        mkdir($fullPath, 0755, true);
        echo "创建目录: $fullPath\n";
    }
}

// 文件映射
$fileMap = [
    // 核心配置文件
    'plugin.json' => '插件配置.json',
    'install.php' => '安装脚本.php',
    'uninstall.php' => '卸载脚本.php',
    
    // 文档文件
    'README_RESUME_MODULE.md' => '插件说明.md',
    'DEPLOYMENT_CHECKLIST.md' => 'docs/部署检查清单.md',
    'resume_plugin_structure.md' => 'docs/插件结构说明.md',
    'PLUGIN_PACKAGE_GUIDE.md' => 'docs/打包指南.md',
    'resume_module_design.md' => 'docs/架构设计.md',
    '简历管理插件整理说明.md' => 'docs/文件整理说明.md',
    
    // 数据库脚本
    'resume_upgrade.sql' => 'sql/安装脚本.sql',
    
    // 后端文件
    'resumeModel.php' => 'files/webmain/model/resumeModel.php',
    'resumeAction.php' => 'files/webmain/main/resume/resumeAction.php',
    'tpl_resume.html' => 'files/webmain/main/resume/tpl_resume.html',
    'tpl_resume_detail.html' => 'files/webmain/main/resume/tpl_resume_detail.html',
    
    // 工具类
    'resume_upload_handler.php' => 'files/webmain/include/ResumeUploadHandler.php',
    'resume_security.php' => 'files/webmain/include/ResumeSecurityManager.php',
    
    // 外链文件
    'resume_public_form.html' => 'files/web/resume/index.html',
    'resume_submit.php' => 'files/web/resume/submit.php',
    
    // 安装脚本
    'install_resume_module.php' => 'legacy/install_resume_module.php'
];

// 复制文件
foreach ($fileMap as $source => $target) {
    $sourcePath = $source;
    $targetPath = $pluginDir . '/' . $target;
    
    if (file_exists($sourcePath)) {
        // 确保目标目录存在
        $targetDir = dirname($targetPath);
        if (!is_dir($targetDir)) {
            mkdir($targetDir, 0755, true);
        }
        
        if (copy($sourcePath, $targetPath)) {
            echo "复制文件: $source → $target\n";
        } else {
            echo "复制文件失败: $source\n";
        }
    } else {
        echo "源文件不存在: $source\n";
    }
}

// 创建保护文件
$htaccessContent = "Options -Indexes\nOrder allow,deny\nAllow from all\n<Files ~ \"\\.(php|phtml|php3|php4|php5|pl|py|jsp|asp|sh|cgi)$\">\n    deny from all\n</Files>\n";
file_put_contents($pluginDir . '/files/upload/resume/.htaccess', $htaccessContent);

$indexContent = '<!DOCTYPE html><html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>';
file_put_contents($pluginDir . '/files/upload/resume/index.html', $indexContent);

$webHtaccess = "<Files ~ \"\\.(php)$\">\n    Order allow,deny\n    Allow from all\n</Files>\n";
file_put_contents($pluginDir . '/files/web/resume/.htaccess', $webHtaccess);

// 创建许可证
$license = "MIT License\n\nCopyright (c) 2025 信呼开发团队\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n";
file_put_contents($pluginDir . '/许可证.txt', $license);

// 创建更新日志
$changelog = "# 更新日志\n\n## [1.0.0] - 2025-06-21\n\n### 新增功能\n- ✅ 完整的简历管理功能\n- ✅ 外链简历填写页面\n- ✅ 后台管理和审核功能\n- ✅ 文件上传和安全控制\n- ✅ 数据统计和导出功能\n- ✅ 简历转员工档案功能\n- ✅ 完善的权限控制系统\n\n### 技术特性\n- 🔧 标准化插件结构\n- 🔧 自动安装和卸载\n- 🔧 安全防护机制\n- 🔧 响应式设计\n- 🔧 API接口支持\n\n### 安全性\n- 🛡️ IP访问频率限制\n- 🛡️ 文件上传安全检查\n- 🛡️ SQL注入防护\n- 🛡️ XSS攻击防护\n- 🛡️ CSRF令牌验证\n";
file_put_contents($pluginDir . '/更新日志.md', $changelog);

// 创建配置文件
$menuConfig = [
    'menus' => [
        [
            'name' => '简历管理',
            'parent_name' => '人事档案',
            'url' => 'main.php?c=resume&a=index',
            'icon' => 'fa-file-text-o',
            'sort' => 10,
            'children' => [
                [
                    'name' => '简历列表',
                    'url' => 'main.php?c=resume&a=index',
                    'icon' => 'fa-list',
                    'sort' => 1
                ],
                [
                    'name' => '简历统计',
                    'url' => 'main.php?c=resume&a=stats',
                    'icon' => 'fa-bar-chart',
                    'sort' => 2
                ]
            ]
        ]
    ]
];
file_put_contents($pluginDir . '/config/菜单配置.json', json_encode($menuConfig, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

$permissionConfig = [
    'permissions' => [
        'resume_view' => '查看简历',
        'resume_edit' => '编辑简历',
        'resume_delete' => '删除简历',
        'resume_audit' => '审核简历',
        'resume_export' => '导出简历',
        'resume_stats' => '查看统计'
    ]
];
file_put_contents($pluginDir . '/config/权限配置.json', json_encode($permissionConfig, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

$defaultSettings = [
    'upload' => [
        'max_file_size' => '10MB',
        'allowed_types' => ['pdf', 'doc', 'docx', 'jpg', 'png'],
        'upload_path' => 'upload/resume/'
    ],
    'security' => [
        'rate_limit' => 10,
        'rate_window' => 3600,
        'enable_captcha' => true
    ],
    'notification' => [
        'email_admin' => true,
        'email_hr' => true
    ]
];
file_put_contents($pluginDir . '/config/默认设置.json', json_encode($defaultSettings, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

echo "\n✅ 插件目录结构创建完成！\n";
echo "📁 插件目录: $pluginDir/\n";
echo "\n下一步：\n";
echo "1. 检查生成的插件目录结构\n";
echo "2. 测试安装脚本\n";
echo "3. 创建压缩包分发\n";
?>
