/* 现代化登录页面样式 - 保持所有原有功能不变 */

/* 全局变量定义 */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
  --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.5);
  --border-radius: 16px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 现代化背景 */
body {
  background: var(--primary-gradient) !important;
  background-attachment: fixed !important;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON>', <PERSON><PERSON>, <PERSON>xygen, Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;
}

/* 背景动画粒子效果 */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes backgroundShift {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.1); }
}

/* 现代化登录容器 */
.lmaisft {
  background: var(--glass-bg) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: var(--border-radius) !important;
  box-shadow: var(--shadow-heavy) !important;
  width: 420px !important;
  max-width: 90vw !important;
  padding: 40px 30px !important;
  transition: var(--transition) !important;
  position: relative;
  overflow: hidden;
}

.lmaisft::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--secondary-gradient);
  opacity: 0.8;
}

.lmaisft:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(31, 38, 135, 0.6);
}

/* Logo现代化样式 */
#imglogo {
  transition: var(--transition) !important;
  border: 3px solid var(--glass-border) !important;
  box-shadow: var(--shadow-light) !important;
  cursor: pointer !important;
}

#imglogo:hover {
  transform: rotate(360deg) scale(1.05);
  box-shadow: var(--shadow-heavy);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 现代化输入框 */
.modern-input-group {
  position: relative;
  margin-bottom: 25px;
}

.modern-input {
  width: 100% !important;
  height: 50px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: 12px !important;
  padding: 15px 20px !important;
  font-size: 16px !important;
  color: var(--text-primary) !important;
  transition: var(--transition) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.modern-input::placeholder {
  color: var(--text-secondary) !important;
  transition: var(--transition);
}

.modern-input:focus {
  outline: none !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  background: rgba(255, 255, 255, 0.15) !important;
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px);
}

.modern-input:focus::placeholder {
  color: transparent;
}

/* 浮动标签效果 */
.floating-label {
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  color: var(--text-secondary);
  font-size: 16px;
  transition: var(--transition);
  pointer-events: none;
  background: transparent;
  padding: 0 5px;
}

.modern-input:focus + .floating-label,
.modern-input:not(:placeholder-shown) + .floating-label {
  top: -8px;
  font-size: 12px;
  color: var(--text-primary);
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border-radius: 4px;
}

/* 现代化按钮 */
.modern-btn {
  width: 100% !important;
  height: 50px !important;
  background: var(--secondary-gradient) !important;
  border: none !important;
  border-radius: 12px !important;
  color: var(--text-primary) !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: var(--transition) !important;
  position: relative !important;
  overflow: hidden !important;
  box-shadow: var(--shadow-light) !important;
}

.modern-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.modern-btn:hover::before {
  left: 100%;
}

.modern-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.modern-btn:active {
  transform: translateY(0);
}

.modern-btn:disabled {
  background: rgba(255, 255, 255, 0.1) !important;
  color: var(--text-secondary) !important;
  cursor: not-allowed !important;
  transform: none !important;
}

/* 现代化复选框 */
.modern-checkbox {
  position: relative;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  color: var(--text-secondary);
  font-size: 14px;
  transition: var(--transition);
}

.modern-checkbox input[type="checkbox"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.modern-checkbox .checkmark {
  width: 20px;
  height: 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--glass-border);
  border-radius: 4px;
  margin-right: 10px;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.modern-checkbox input:checked + .checkmark {
  background: var(--secondary-gradient);
  border-color: transparent;
}

.modern-checkbox .checkmark::after {
  content: '✓';
  color: white;
  font-size: 12px;
  opacity: 0;
  transition: var(--transition);
}

.modern-checkbox input:checked + .checkmark::after {
  opacity: 1;
}

/* 链接样式 */
.modern-link {
  color: var(--text-primary) !important;
  text-decoration: none !important;
  font-size: 14px !important;
  transition: var(--transition) !important;
  position: relative !important;
}

.modern-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background: var(--text-primary);
  transition: var(--transition);
}

.modern-link:hover::after {
  width: 100%;
}

.modern-link:hover {
  color: rgba(255, 255, 255, 1) !important;
}

/* 消息提示现代化 */
#msgview {
  margin-top: 15px;
  color: var(--text-primary);
  font-size: 14px;
  transition: var(--transition);
  min-height: 0;
}

/* 消息背景样式 - 通过JavaScript控制 */
#msgview.show-background {
  padding: 10px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
}

/* 注册链接样式 - 保持简洁 */
#msgview .modern-link {
  opacity: 0.8;
  transition: var(--transition);
  display: inline-block;
  margin-top: 5px;
}

#msgview .modern-link:hover {
  opacity: 1;
}

/* 标题现代化 */
.modern-title {
  color: var(--text-primary) !important;
  font-size: 32px !important;
  font-weight: 300 !important;
  margin-bottom: 10px !important;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
  letter-spacing: 1px !important;
}



/* 验证码获取按钮 */
.modern-code-btn {
  height: 50px !important;
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: 0 12px 12px 0 !important;
  color: var(--text-primary) !important;
  font-size: 14px !important;
  cursor: pointer !important;
  transition: var(--transition) !important;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.modern-code-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.4) !important;
}

.modern-code-btn:disabled {
  background: rgba(255, 255, 255, 0.05) !important;
  color: var(--text-secondary) !important;
  cursor: not-allowed !important;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .lmaisft {
    width: 95vw !important;
    padding: 30px 20px !important;
  }
  
  .modern-title {
    font-size: 28px !important;
  }
  
  .modern-input {
    height: 45px !important;
    font-size: 15px !important;
  }
  
  .modern-btn {
    height: 45px !important;
    font-size: 15px !important;
  }
}

/* 加载动画 */
@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.loading {
  animation: pulse 1.5s ease-in-out infinite;
}

/* 平滑进入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}
