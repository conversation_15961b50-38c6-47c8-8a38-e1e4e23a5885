<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- 允许访问HTML和PHP文件 -->
        <security>
            <requestFiltering>
                <fileExtensions>
                    <remove fileExtension=".html" />
                    <remove fileExtension=".php" />
                    <add fileExtension=".html" allowed="true" />
                    <add fileExtension=".php" allowed="true" />
                    <!-- 禁止访问其他类型文件 -->
                    <add fileExtension=".txt" allowed="false" />
                    <add fileExtension=".log" allowed="false" />
                    <add fileExtension=".sql" allowed="false" />
                    <add fileExtension=".md" allowed="false" />
                    <add fileExtension=".json" allowed="false" />
                </fileExtensions>
                <!-- 禁止访问隐藏文件 -->
                <hiddenSegments>
                    <add segment="." />
                </hiddenSegments>
            </requestFiltering>
        </security>
        
        <!-- 设置安全头 -->
        <httpProtocol>
            <customHeaders>
                <add name="X-Content-Type-Options" value="nosniff" />
                <add name="X-Frame-Options" value="SAMEORIGIN" />
                <add name="X-XSS-Protection" value="1; mode=block" />
            </customHeaders>
        </httpProtocol>
        
        <!-- 默认文档 -->
        <defaultDocument>
            <files>
                <add value="index.html" />
                <add value="index.php" />
            </files>
        </defaultDocument>
    </system.webServer>
</configuration>
