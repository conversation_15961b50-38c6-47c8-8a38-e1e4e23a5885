<?php
/**
 * 简历管理控制器
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */
class resumeClassAction extends Action
{
    private $resumeModel;

    public function __construct()
    {
        parent::__construct();
        $this->resumeModel = m('resume');
    }

    /**
     * 默认页面 - 简历列表
     */
    public function defaultAction()
    {
        $this->listAction();
    }

    /**
     * 简历列表页面
     */
    public function listAction()
    {
        $this->assign('title', '简历管理');
    }

    /**
     * 获取简历列表数据 (Ajax)
     */
    public function dataAjax()
    {
        $page = (int)$this->post('page', 1);
        $limit = (int)$this->post('limit', 20);
        $key = $this->post('key', '');
        $status = $this->post('status', '');
        $position = $this->post('position', '');
        $startDate = $this->post('start_date', '');
        $endDate = $this->post('end_date', '');

        // 构建查询条件
        $where = '1=1';
        if (!empty($key)) {
            $where .= " AND (`name` LIKE '%{$key}%' OR `mobile` LIKE '%{$key}%' OR `email` LIKE '%{$key}%')";
        }
        if ($status !== '') {
            $where .= " AND `status`={$status}";
        }
        if (!empty($position)) {
            $where .= " AND `position` LIKE '%{$position}%'";
        }
        if (!empty($startDate)) {
            $where .= " AND DATE(`submit_time`) >= '{$startDate}'";
        }
        if (!empty($endDate)) {
            $where .= " AND DATE(`submit_time`) <= '{$endDate}'";
        }

        // 分页计算
        $total = $this->resumeModel->rows($where);
        $offset = ($page - 1) * $limit;

        // 获取数据
        $fields = 'id,name,mobile,email,position,status,submit_time,review_time,reviewer_name,remark';
        $order = 'submit_time DESC';
        $rows = $this->resumeModel->getResumeList("{$where} LIMIT {$offset},{$limit}", $fields, $order);

        // 处理数据
        foreach ($rows as $k => $row) {
            $rows[$k]['status_text'] = $this->resumeModel->getStatusText($row['status']);
            $rows[$k]['submit_time'] = date('Y-m-d H:i', strtotime($row['submit_time']));
            $rows[$k]['review_time'] = !empty($row['review_time']) ? date('Y-m-d H:i', strtotime($row['review_time'])) : '';
        }

        $this->returnjson(array(
            'total' => $total,
            'rows' => $rows
        ));
    }

    /**
     * 简历详情页面
     */
    public function detailAction()
    {
        $id = (int)$this->get('id');
        if (!$id) {
            $this->showmsg('参数错误');
            return;
        }

        $resume = $this->resumeModel->getResumeDetail($id);
        if (!$resume) {
            $this->showmsg('简历不存在');
            return;
        }

        // 处理数据
        $resume['status_text'] = $this->resumeModel->getStatusText($resume['status']);
        $resume['gender_text'] = $this->resumeModel->getGenderText($resume['gender']);
        $resume['submit_time'] = date('Y-m-d H:i:s', strtotime($resume['submit_time']));
        $resume['review_time'] = !empty($resume['review_time']) ? date('Y-m-d H:i:s', strtotime($resume['review_time'])) : '';

        $this->assign('resume', $resume);
        $this->assign('title', '简历详情 - ' . $resume['name']);
    }

    /**
     * 审核简历 (Ajax)
     */
    public function reviewAjax()
    {
        $id = (int)$this->post('id');
        $status = (int)$this->post('status');
        $remark = $this->post('remark', '');

        if (!$id || !in_array($status, [1, 2])) {
            $this->returnjson(array('success' => false, 'msg' => '参数错误'));
            return;
        }

        $result = $this->resumeModel->reviewResume($id, $status, $remark);
        
        if ($result) {
            $statusText = $status == 1 ? '通过' : '拒绝';
            $this->returnjson(array('success' => true, 'msg' => "审核{$statusText}成功"));
        } else {
            $this->returnjson(array('success' => false, 'msg' => '审核失败'));
        }
    }

    /**
     * 批量审核 (Ajax)
     */
    public function batchReviewAjax()
    {
        $ids = $this->post('ids', '');
        $status = (int)$this->post('status');
        $remark = $this->post('remark', '');

        if (empty($ids) || !in_array($status, [1, 2])) {
            $this->returnjson(array('success' => false, 'msg' => '参数错误'));
            return;
        }

        $idArray = explode(',', $ids);
        $successCount = 0;

        foreach ($idArray as $id) {
            $id = (int)$id;
            if ($id > 0) {
                if ($this->resumeModel->reviewResume($id, $status, $remark)) {
                    $successCount++;
                }
            }
        }

        $statusText = $status == 1 ? '通过' : '拒绝';
        $this->returnjson(array(
            'success' => true, 
            'msg' => "批量审核完成，{$successCount}条记录审核{$statusText}"
        ));
    }

    /**
     * 转换为员工档案 (Ajax)
     */
    public function convertToEmployeeAjax()
    {
        $id = (int)$this->post('id');
        $deptId = (int)$this->post('dept_id', 0);
        $deptName = $this->post('dept_name', '');

        if (!$id) {
            $this->returnjson(array('success' => false, 'msg' => '参数错误'));
            return;
        }

        $employeeData = array();
        if ($deptId > 0) {
            $employeeData['deptid'] = $deptId;
        }
        if (!empty($deptName)) {
            $employeeData['deptname'] = $deptName;
        }

        $userId = $this->resumeModel->convertToEmployee($id, $employeeData);
        
        if ($userId) {
            $this->returnjson(array('success' => true, 'msg' => '转换为员工档案成功', 'user_id' => $userId));
        } else {
            $this->returnjson(array('success' => false, 'msg' => '转换失败，可能手机号已存在'));
        }
    }

    /**
     * 删除简历 (Ajax)
     */
    public function deleteAjax()
    {
        $id = (int)$this->post('id');
        
        if (!$id) {
            $this->returnjson(array('success' => false, 'msg' => '参数错误'));
            return;
        }

        // 获取简历信息
        $resume = $this->resumeModel->getone($id);
        if (!$resume) {
            $this->returnjson(array('success' => false, 'msg' => '简历不存在'));
            return;
        }

        // 删除简历文件
        if (!empty($resume['resume_file']) && file_exists($resume['resume_file'])) {
            @unlink($resume['resume_file']);
        }

        // 删除数据库记录
        $result = $this->resumeModel->delete($id);
        
        if ($result) {
            // 记录操作日志
            $this->resumeModel->addResumeLog($id, 'delete', $resume['status'], null, '删除简历');
            $this->returnjson(array('success' => true, 'msg' => '删除成功'));
        } else {
            $this->returnjson(array('success' => false, 'msg' => '删除失败'));
        }
    }

    /**
     * 简历统计页面
     */
    public function statsAction()
    {
        $startDate = $this->get('start_date', date('Y-m-01'));
        $endDate = $this->get('end_date', date('Y-m-d'));

        // 获取统计数据
        $stats = $this->resumeModel->getResumeStats($startDate, $endDate);
        $dailyStats = $this->resumeModel->getResumeStatsByDate($startDate, $endDate);

        $this->assign('stats', $stats);
        $this->assign('daily_stats', $dailyStats);
        $this->assign('start_date', $startDate);
        $this->assign('end_date', $endDate);
        $this->assign('title', '简历统计');
    }

    /**
     * 获取统计数据 (Ajax)
     */
    public function getStatsAjax()
    {
        $startDate = $this->post('start_date', '');
        $endDate = $this->post('end_date', '');

        $stats = $this->resumeModel->getResumeStats($startDate, $endDate);
        $dailyStats = $this->resumeModel->getResumeStatsByDate($startDate, $endDate);

        $this->returnjson(array(
            'success' => true,
            'stats' => $stats,
            'daily_stats' => $dailyStats
        ));
    }

    /**
     * 导出简历数据
     */
    public function exportAction()
    {
        $status = $this->get('status', '');
        $startDate = $this->get('start_date', '');
        $endDate = $this->get('end_date', '');

        // 构建查询条件
        $where = '1=1';
        if ($status !== '') {
            $where .= " AND `status`={$status}";
        }
        if (!empty($startDate)) {
            $where .= " AND DATE(`submit_time`) >= '{$startDate}'";
        }
        if (!empty($endDate)) {
            $where .= " AND DATE(`submit_time`) <= '{$endDate}'";
        }

        // 获取数据
        $fields = 'name,mobile,email,position,status,submit_time,review_time,reviewer_name,remark';
        $rows = $this->resumeModel->getResumeList($where, $fields);

        // 处理数据
        foreach ($rows as $k => $row) {
            $rows[$k]['status'] = $this->resumeModel->getStatusText($row['status']);
            $rows[$k]['submit_time'] = date('Y-m-d H:i:s', strtotime($row['submit_time']));
            $rows[$k]['review_time'] = !empty($row['review_time']) ? date('Y-m-d H:i:s', strtotime($row['review_time'])) : '';
        }

        // 设置表头
        $headers = array('姓名', '手机号', '邮箱', '应聘职位', '状态', '提交时间', '审核时间', '审核人', '备注');

        // 导出Excel
        $this->exportExcel($rows, $headers, '简历数据_' . date('YmdHis'));
    }

    /**
     * 简历审核页面
     */
    public function reviewAction()
    {
        $this->assign('title', '简历审核');
    }

    /**
     * 获取待审核简历数据 (Ajax)
     */
    public function getPendingReviewAjax()
    {
        $page = (int)$this->post('page', 1);
        $limit = (int)$this->post('limit', 20);

        // 只获取待审核的简历
        $where = 'status=0';
        $total = $this->resumeModel->rows($where);
        $offset = ($page - 1) * $limit;

        $fields = 'id,name,mobile,email,position,submit_time';
        $order = 'submit_time ASC';
        $rows = $this->resumeModel->getResumeList("{$where} LIMIT {$offset},{$limit}", $fields, $order);

        // 处理数据
        foreach ($rows as $k => $row) {
            $rows[$k]['submit_time'] = date('Y-m-d H:i', strtotime($row['submit_time']));
        }

        $this->returnjson(array(
            'total' => $total,
            'rows' => $rows
        ));
    }
}
