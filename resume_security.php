<?php
/**
 * 简历管理安全控制类
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */

class ResumeSecurityManager
{
    private $config;
    private $db;
    
    public function __construct($db = null)
    {
        $this->db = $db;
        $this->initConfig();
    }
    
    /**
     * 初始化安全配置
     */
    private function initConfig()
    {
        $this->config = [
            // 访问频率限制
            'rate_limit' => [
                'submit_per_hour' => 10,        // 每小时最多提交10次
                'submit_per_day' => 50,         // 每天最多提交50次
                'view_per_minute' => 30,        // 每分钟最多访问30次
                'api_per_minute' => 60          // API每分钟最多60次
            ],
            
            // IP黑名单检查
            'ip_blacklist' => [
                'enable' => true,
                'auto_ban' => true,
                'ban_threshold' => 100,         // 超过100次异常请求自动封禁
                'ban_duration' => 3600          // 封禁1小时
            ],
            
            // 数据验证规则
            'validation' => [
                'name_max_length' => 50,
                'mobile_pattern' => '/^1[3-9]\d{9}$/',
                'email_max_length' => 200,
                'text_max_length' => 5000,
                'position_max_length' => 100
            ],
            
            // 安全头设置
            'security_headers' => [
                'X-Content-Type-Options' => 'nosniff',
                'X-Frame-Options' => 'DENY',
                'X-XSS-Protection' => '1; mode=block',
                'Referrer-Policy' => 'strict-origin-when-cross-origin'
            ]
        ];
    }
    
    /**
     * 检查访问权限
     * 
     * @param string $action 操作类型
     * @param array $params 参数
     * @return bool
     */
    public function checkAccess($action, $params = [])
    {
        try {
            // 设置安全头
            $this->setSecurityHeaders();
            
            // IP黑名单检查
            if (!$this->checkIPBlacklist()) {
                $this->logSecurityEvent('ip_blocked', ['ip' => $this->getClientIP()]);
                return false;
            }
            
            // 访问频率限制
            if (!$this->checkRateLimit($action)) {
                $this->logSecurityEvent('rate_limit_exceeded', [
                    'action' => $action,
                    'ip' => $this->getClientIP()
                ]);
                return false;
            }
            
            // 根据操作类型进行具体检查
            switch ($action) {
                case 'submit_resume':
                    return $this->checkSubmitAccess($params);
                    
                case 'view_resume':
                    return $this->checkViewAccess($params);
                    
                case 'admin_access':
                    return $this->checkAdminAccess($params);
                    
                default:
                    return true;
            }
            
        } catch (Exception $e) {
            $this->logSecurityEvent('access_check_error', [
                'action' => $action,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
    
    /**
     * 设置安全响应头
     */
    private function setSecurityHeaders()
    {
        foreach ($this->config['security_headers'] as $header => $value) {
            if (!headers_sent()) {
                header($header . ': ' . $value);
            }
        }
    }
    
    /**
     * 检查IP黑名单
     * 
     * @return bool
     */
    private function checkIPBlacklist()
    {
        if (!$this->config['ip_blacklist']['enable']) {
            return true;
        }
        
        $ip = $this->getClientIP();
        
        // 检查数据库中的黑名单
        if ($this->db) {
            $blacklist = $this->db->getone(
                "SELECT * FROM xinhu_ip_blacklist 
                 WHERE ip = '{$ip}' AND (expire_time IS NULL OR expire_time > NOW())"
            );
            
            if ($blacklist) {
                return false;
            }
        }
        
        // 检查是否需要自动封禁
        if ($this->config['ip_blacklist']['auto_ban']) {
            $this->checkAutoBan($ip);
        }
        
        return true;
    }
    
    /**
     * 检查自动封禁
     * 
     * @param string $ip IP地址
     */
    private function checkAutoBan($ip)
    {
        if (!$this->db) return;
        
        // 统计最近1小时的异常请求次数
        $count = $this->db->getone(
            "SELECT COUNT(*) as count FROM xinhu_security_log 
             WHERE ip = '{$ip}' AND event_type IN ('rate_limit_exceeded', 'invalid_request', 'security_violation') 
             AND created_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)"
        );
        
        if ($count && $count['count'] >= $this->config['ip_blacklist']['ban_threshold']) {
            // 自动封禁IP
            $expireTime = date('Y-m-d H:i:s', time() + $this->config['ip_blacklist']['ban_duration']);
            
            $this->db->insert('xinhu_ip_blacklist', [
                'ip' => $ip,
                'reason' => 'Auto ban due to excessive violations',
                'expire_time' => $expireTime,
                'created_time' => date('Y-m-d H:i:s')
            ]);
            
            $this->logSecurityEvent('auto_ban', ['ip' => $ip, 'count' => $count['count']]);
        }
    }
    
    /**
     * 检查访问频率限制
     * 
     * @param string $action 操作类型
     * @return bool
     */
    private function checkRateLimit($action)
    {
        if (!$this->db) return true;
        
        $ip = $this->getClientIP();
        $limits = $this->config['rate_limit'];
        
        switch ($action) {
            case 'submit_resume':
                // 检查每小时提交次数
                $hourlyCount = $this->db->getone(
                    "SELECT COUNT(*) as count FROM xinhu_resume 
                     WHERE ip_address = '{$ip}' AND submit_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)"
                );
                
                if ($hourlyCount && $hourlyCount['count'] >= $limits['submit_per_hour']) {
                    return false;
                }
                
                // 检查每天提交次数
                $dailyCount = $this->db->getone(
                    "SELECT COUNT(*) as count FROM xinhu_resume 
                     WHERE ip_address = '{$ip}' AND DATE(submit_time) = CURDATE()"
                );
                
                if ($dailyCount && $dailyCount['count'] >= $limits['submit_per_day']) {
                    return false;
                }
                break;
                
            case 'view_resume':
                // 检查每分钟访问次数
                $minuteCount = $this->db->getone(
                    "SELECT COUNT(*) as count FROM xinhu_access_log 
                     WHERE ip = '{$ip}' AND action = 'view_resume' 
                     AND created_time > DATE_SUB(NOW(), INTERVAL 1 MINUTE)"
                );
                
                if ($minuteCount && $minuteCount['count'] >= $limits['view_per_minute']) {
                    return false;
                }
                break;
        }
        
        return true;
    }
    
    /**
     * 检查简历提交权限
     * 
     * @param array $params 参数
     * @return bool
     */
    private function checkSubmitAccess($params)
    {
        // 检查必要参数
        $requiredFields = ['name', 'mobile', 'position'];
        foreach ($requiredFields as $field) {
            if (empty($params[$field])) {
                $this->logSecurityEvent('invalid_request', [
                    'reason' => 'Missing required field: ' . $field,
                    'ip' => $this->getClientIP()
                ]);
                return false;
            }
        }
        
        // 验证数据格式
        if (!$this->validateData($params)) {
            return false;
        }
        
        // 检查重复提交
        if ($this->checkDuplicateSubmission($params)) {
            $this->logSecurityEvent('duplicate_submission', [
                'mobile' => $params['mobile'],
                'ip' => $this->getClientIP()
            ]);
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查查看权限
     * 
     * @param array $params 参数
     * @return bool
     */
    private function checkViewAccess($params)
    {
        // 记录访问日志
        $this->logAccess('view_resume', $params);
        
        return true;
    }
    
    /**
     * 检查管理员权限
     * 
     * @param array $params 参数
     * @return bool
     */
    private function checkAdminAccess($params)
    {
        // 这里应该集成信呼系统的权限检查
        // 暂时返回true，实际使用时需要检查用户权限
        return true;
    }
    
    /**
     * 验证数据格式
     * 
     * @param array $data 数据
     * @return bool
     */
    private function validateData($data)
    {
        $rules = $this->config['validation'];
        
        // 验证姓名
        if (isset($data['name']) && strlen($data['name']) > $rules['name_max_length']) {
            $this->logSecurityEvent('validation_failed', ['field' => 'name', 'reason' => 'too_long']);
            return false;
        }
        
        // 验证手机号
        if (isset($data['mobile']) && !preg_match($rules['mobile_pattern'], $data['mobile'])) {
            $this->logSecurityEvent('validation_failed', ['field' => 'mobile', 'reason' => 'invalid_format']);
            return false;
        }
        
        // 验证邮箱
        if (isset($data['email']) && !empty($data['email'])) {
            if (strlen($data['email']) > $rules['email_max_length'] || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
                $this->logSecurityEvent('validation_failed', ['field' => 'email', 'reason' => 'invalid_format']);
                return false;
            }
        }
        
        // 验证文本字段长度
        $textFields = ['work_experience', 'education_background', 'skills', 'self_introduction'];
        foreach ($textFields as $field) {
            if (isset($data[$field]) && strlen($data[$field]) > $rules['text_max_length']) {
                $this->logSecurityEvent('validation_failed', ['field' => $field, 'reason' => 'too_long']);
                return false;
            }
        }
        
        // 验证职位
        if (isset($data['position']) && strlen($data['position']) > $rules['position_max_length']) {
            $this->logSecurityEvent('validation_failed', ['field' => 'position', 'reason' => 'too_long']);
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查重复提交
     * 
     * @param array $data 数据
     * @return bool
     */
    private function checkDuplicateSubmission($data)
    {
        if (!$this->db || empty($data['mobile'])) {
            return false;
        }
        
        // 检查相同手机号在短时间内的提交
        $recent = $this->db->getone(
            "SELECT id FROM xinhu_resume 
             WHERE mobile = '{$data['mobile']}' 
             AND submit_time > DATE_SUB(NOW(), INTERVAL 10 MINUTE)"
        );
        
        return !empty($recent);
    }
    
    /**
     * 数据清理和过滤
     * 
     * @param mixed $data 数据
     * @return mixed
     */
    public function sanitizeData($data)
    {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeData'], $data);
        }
        
        if (is_string($data)) {
            // 移除危险字符
            $data = trim($data);
            $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
            
            // 移除潜在的脚本代码
            $data = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $data);
            $data = preg_replace('/javascript:/i', '', $data);
            $data = preg_replace('/on\w+\s*=/i', '', $data);
            
            return $data;
        }
        
        return $data;
    }
    
    /**
     * 生成CSRF令牌
     * 
     * @return string
     */
    public function generateCSRFToken()
    {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        $token = bin2hex(random_bytes(32));
        $_SESSION['csrf_token'] = $token;
        $_SESSION['csrf_token_time'] = time();
        
        return $token;
    }
    
    /**
     * 验证CSRF令牌
     * 
     * @param string $token 令牌
     * @return bool
     */
    public function validateCSRFToken($token)
    {
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }
        
        if (empty($_SESSION['csrf_token']) || empty($_SESSION['csrf_token_time'])) {
            return false;
        }
        
        // 检查令牌是否过期（30分钟）
        if (time() - $_SESSION['csrf_token_time'] > 1800) {
            unset($_SESSION['csrf_token'], $_SESSION['csrf_token_time']);
            return false;
        }
        
        return hash_equals($_SESSION['csrf_token'], $token);
    }
    
    /**
     * 获取客户端IP地址
     * 
     * @return string
     */
    private function getClientIP()
    {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ips = explode(',', $_SERVER[$key]);
                $ip = trim($ips[0]);
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * 记录安全事件
     * 
     * @param string $eventType 事件类型
     * @param array $data 事件数据
     */
    private function logSecurityEvent($eventType, $data = [])
    {
        if (!$this->db) return;
        
        $logData = [
            'event_type' => $eventType,
            'ip' => $this->getClientIP(),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? '',
            'event_data' => json_encode($data),
            'created_time' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('xinhu_security_log', $logData);
    }
    
    /**
     * 记录访问日志
     * 
     * @param string $action 操作
     * @param array $params 参数
     */
    private function logAccess($action, $params = [])
    {
        if (!$this->db) return;
        
        $logData = [
            'ip' => $this->getClientIP(),
            'action' => $action,
            'params' => json_encode($params),
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'created_time' => date('Y-m-d H:i:s')
        ];
        
        $this->db->insert('xinhu_access_log', $logData);
    }
    
    /**
     * 清理过期日志
     * 
     * @param int $days 保留天数
     * @return int 清理的记录数
     */
    public function cleanupLogs($days = 30)
    {
        if (!$this->db) return 0;
        
        $cleanupDate = date('Y-m-d', time() - ($days * 24 * 60 * 60));
        
        $count1 = $this->db->delete('xinhu_security_log', "DATE(created_time) < '{$cleanupDate}'");
        $count2 = $this->db->delete('xinhu_access_log', "DATE(created_time) < '{$cleanupDate}'");
        
        return $count1 + $count2;
    }
}
