<?php
/**
 * 简历管理插件卸载脚本
 * 
 * <AUTHOR>
 * @version 1.0
 * @date 2025-06-21
 */

// 防止直接访问
if (!defined('XINHU_PATH')) {
    define('XINHU_PATH', dirname(dirname(__DIR__)));
}

class ResumePluginUninstaller
{
    private $pluginPath;
    private $config;
    private $db;
    private $uninstallLog = [];
    private $xinhuPath;
    private $keepData = false;
    
    public function __construct($pluginPath = null, $keepData = false)
    {
        $this->pluginPath = $pluginPath ?: dirname(__FILE__);
        $this->xinhuPath = XINHU_PATH;
        $this->keepData = $keepData;
        $this->loadConfig();
        $this->initDatabase();
    }
    
    /**
     * 加载插件配置
     */
    private function loadConfig()
    {
        $configFile = $this->pluginPath . '/plugin.json';
        if (!file_exists($configFile)) {
            throw new Exception('插件配置文件不存在');
        }
        
        $this->config = json_decode(file_get_contents($configFile), true);
        if (!$this->config) {
            throw new Exception('插件配置文件格式错误');
        }
    }
    
    /**
     * 初始化数据库连接
     */
    private function initDatabase()
    {
        try {
            require_once $this->xinhuPath . '/webmain/model/base.php';
            global $rock;
            $this->db = $rock->db;
        } catch (Exception $e) {
            throw new Exception('数据库连接失败: ' . $e->getMessage());
        }
    }
    
    /**
     * 执行卸载
     */
    public function uninstall()
    {
        try {
            $this->log('开始卸载插件: ' . $this->config['display_name']);
            
            // 1. 检查插件是否已安装
            $this->checkIfInstalled();
            
            // 2. 执行卸载前钩子
            $this->executeHook('before_uninstall');
            
            // 3. 备份数据（如果需要保留）
            if ($this->keepData) {
                $this->backupData();
            }
            
            // 4. 删除菜单
            $this->removeMenus();
            
            // 5. 删除文件
            $this->removeFiles();
            
            // 6. 删除数据库（如果不保留数据）
            if (!$this->keepData) {
                $this->removeDatabase();
            }
            
            // 7. 清理配置
            $this->cleanupSettings();
            
            // 8. 删除插件记录
            $this->removePluginRecord();
            
            $this->log('插件卸载完成');
            return $this->getResult(true, '卸载成功');
            
        } catch (Exception $e) {
            $this->log('卸载失败: ' . $e->getMessage(), 'error');
            return $this->getResult(false, $e->getMessage());
        }
    }
    
    /**
     * 检查插件是否已安装
     */
    private function checkIfInstalled()
    {
        $plugin = $this->db->getone(
            "SELECT * FROM xinhu_plugin WHERE name = '{$this->config['name']}'"
        );
        
        if (!$plugin) {
            throw new Exception('插件未安装或已被删除');
        }
        
        $this->log('找到已安装的插件，版本: ' . $plugin['version']);
    }
    
    /**
     * 备份数据
     */
    private function backupData()
    {
        $this->log('备份插件数据...');
        
        $backupDir = $this->xinhuPath . '/upload/backup/plugin_uninstall_' . date('YmdHis') . '/';
        if (!is_dir($backupDir)) {
            mkdir($backupDir, 0755, true);
        }
        
        // 备份数据表
        if (isset($this->config['database']['tables'])) {
            foreach ($this->config['database']['tables'] as $table) {
                $tableName = is_array($table) ? $table['name'] : $table;
                
                // 检查表是否存在
                $tableExists = $this->db->getone("SHOW TABLES LIKE '{$tableName}'");
                if ($tableExists) {
                    $data = $this->db->getall("SELECT * FROM {$tableName}");
                    $backupFile = $backupDir . $tableName . '.json';
                    file_put_contents($backupFile, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
                    $this->log('备份数据表: ' . $tableName);
                }
            }
        }
        
        // 备份上传的文件
        $uploadDir = $this->xinhuPath . '/upload/resume/';
        if (is_dir($uploadDir)) {
            $backupUploadDir = $backupDir . 'upload_files/';
            $this->copyDirectory($uploadDir, $backupUploadDir);
            $this->log('备份上传文件');
        }
        
        $this->log('数据备份完成: ' . $backupDir);
    }
    
    /**
     * 删除菜单
     */
    private function removeMenus()
    {
        $this->log('删除菜单项...');
        
        foreach ($this->config['menus'] as $menu) {
            $this->removeMenu($menu);
        }
    }
    
    /**
     * 删除单个菜单及其子菜单
     */
    private function removeMenu($menuConfig)
    {
        // 删除子菜单
        if (isset($menuConfig['children'])) {
            foreach ($menuConfig['children'] as $child) {
                $this->db->delete('xinhu_menu', "name = '{$child['name']}'");
                $this->log('删除子菜单: ' . $child['name']);
            }
        }
        
        // 删除主菜单
        $this->db->delete('xinhu_menu', "name = '{$menuConfig['name']}'");
        $this->log('删除菜单: ' . $menuConfig['name']);
    }
    
    /**
     * 删除文件
     */
    private function removeFiles()
    {
        $this->log('删除插件文件...');
        
        foreach ($this->config['files']['copy'] as $file) {
            $targetPath = $this->xinhuPath . '/' . $file['to'];
            
            if (file_exists($targetPath)) {
                if (is_dir($targetPath)) {
                    $this->removeDirectory($targetPath);
                    $this->log('删除目录: ' . $file['to']);
                } else {
                    unlink($targetPath);
                    $this->log('删除文件: ' . $file['to']);
                }
            }
        }
        
        // 删除创建的目录（如果为空）
        foreach ($this->config['files']['create_dirs'] as $dir) {
            $dirPath = is_array($dir) ? $dir['path'] : $dir;
            $fullPath = $this->xinhuPath . '/' . $dirPath;
            
            if (is_dir($fullPath)) {
                // 只删除空目录
                $files = array_diff(scandir($fullPath), ['.', '..']);
                if (empty($files)) {
                    rmdir($fullPath);
                    $this->log('删除空目录: ' . $dirPath);
                } else {
                    $this->log('目录不为空，保留: ' . $dirPath, 'warning');
                }
            }
        }
    }
    
    /**
     * 删除数据库
     */
    private function removeDatabase()
    {
        $this->log('删除数据库表...');
        
        if (isset($this->config['database']['tables'])) {
            // 按相反顺序删除表（考虑外键约束）
            $tables = array_reverse($this->config['database']['tables']);
            
            foreach ($tables as $table) {
                $tableName = is_array($table) ? $table['name'] : $table;
                
                try {
                    $this->db->query("DROP TABLE IF EXISTS {$tableName}");
                    $this->log('删除数据表: ' . $tableName);
                } catch (Exception $e) {
                    $this->log('删除数据表失败: ' . $tableName . ' - ' . $e->getMessage(), 'warning');
                }
            }
        }
    }
    
    /**
     * 清理配置
     */
    private function cleanupSettings()
    {
        $this->log('清理插件配置...');
        
        // 删除插件相关的系统选项
        $this->db->delete('xinhu_option', "name LIKE 'resume_%'");
        
        // 清理权限相关配置
        if (isset($this->config['permissions'])) {
            foreach ($this->config['permissions'] as $permission) {
                $permKey = is_array($permission) ? $permission['key'] : $permission;
                // 这里可以添加删除权限记录的逻辑
            }
        }
        
        $this->log('配置清理完成');
    }
    
    /**
     * 删除插件记录
     */
    private function removePluginRecord()
    {
        $this->log('删除插件安装记录...');
        
        $this->db->delete('xinhu_plugin', "name = '{$this->config['name']}'");
    }
    
    /**
     * 执行钩子
     */
    private function executeHook($hookName)
    {
        if (isset($this->config['hooks'][$hookName])) {
            $method = $this->config['hooks'][$hookName];
            if (method_exists($this, $method)) {
                $this->$method();
            }
        }
    }
    
    /**
     * 卸载前钩子
     */
    private function beforeUninstall()
    {
        $this->log('执行卸载前处理...');
        
        // 检查是否有依赖此插件的其他插件
        // 这里可以添加依赖检查逻辑
        
        // 通知相关模块插件即将卸载
        // 这里可以添加通知逻辑
    }
    
    /**
     * 递归删除目录
     */
    private function removeDirectory($dir)
    {
        if (!is_dir($dir)) {
            return;
        }
        
        $files = array_diff(scandir($dir), ['.', '..']);
        
        foreach ($files as $file) {
            $path = $dir . '/' . $file;
            if (is_dir($path)) {
                $this->removeDirectory($path);
            } else {
                unlink($path);
            }
        }
        
        rmdir($dir);
    }
    
    /**
     * 复制目录（用于备份）
     */
    private function copyDirectory($source, $target)
    {
        if (!is_dir($source)) {
            return;
        }
        
        if (!is_dir($target)) {
            mkdir($target, 0755, true);
        }
        
        $iterator = new RecursiveIteratorIterator(
            new RecursiveDirectoryIterator($source, RecursiveDirectoryIterator::SKIP_DOTS),
            RecursiveIteratorIterator::SELF_FIRST
        );
        
        foreach ($iterator as $item) {
            $targetPath = $target . DIRECTORY_SEPARATOR . $iterator->getSubPathName();
            
            if ($item->isDir()) {
                if (!is_dir($targetPath)) {
                    mkdir($targetPath, 0755, true);
                }
            } else {
                copy($item, $targetPath);
            }
        }
    }
    
    /**
     * 添加日志
     */
    private function log($message, $level = 'info')
    {
        $this->uninstallLog[] = [
            'time' => date('Y-m-d H:i:s'),
            'level' => $level,
            'message' => $message
        ];
    }
    
    /**
     * 获取结果
     */
    private function getResult($success, $message)
    {
        return [
            'success' => $success,
            'message' => $message,
            'plugin' => $this->config['display_name'],
            'version' => $this->config['version'],
            'keep_data' => $this->keepData,
            'log' => $this->uninstallLog
        ];
    }
}

// 如果直接访问此文件，执行卸载
if (basename($_SERVER['PHP_SELF']) == basename(__FILE__)) {
    try {
        $keepData = isset($_GET['keep_data']) && $_GET['keep_data'] == '1';
        $uninstaller = new ResumePluginUninstaller(null, $keepData);
        $result = $uninstaller->uninstall();
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        header('Content-Type: application/json; charset=utf-8');
        http_response_code(500);
        echo json_encode([
            'success' => false,
            'message' => $e->getMessage(),
            'plugin' => '简历管理模块',
            'version' => '1.0.0'
        ], JSON_UNESCAPED_UNICODE);
    }
}
