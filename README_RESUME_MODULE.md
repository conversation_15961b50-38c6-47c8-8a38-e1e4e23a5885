# 信呼系统简历管理插件

## 📋 插件简介

简历管理插件是为信呼OA系统开发的标准化人力资源管理扩展插件，提供完整的在线简历收集、管理和审核功能。

### 主要功能

- ✅ **外链简历填写** - 公开访问的简历提交页面
- ✅ **后台管理系统** - 完整的简历管理界面
- ✅ **审核流程** - 简历审核和状态管理
- ✅ **文件上传** - 安全的简历文件上传功能
- ✅ **数据统计** - 简历提交和审核统计
- ✅ **员工转换** - 简历转换为员工档案
- ✅ **权限控制** - 完善的安全和权限管理

## 📦 插件结构

```
xinhu_resume_plugin_v1.0/
├── plugin.json                      # 插件配置文件
├── install.php                      # 安装脚本
├── uninstall.php                    # 卸载脚本
├── README.md                        # 插件说明
├── sql/
│   └── install.sql                  # 数据库安装脚本
├── files/
│   ├── webmain/                     # 后端文件
│   ├── web/resume/                  # 外链文件
│   └── upload/resume/               # 上传目录
└── docs/                            # 文档目录
```

## 🚀 安装方法

### 系统要求

- 信呼OA系统 2.5.0 或更高版本
- PHP 7.0 或更高版本
- MySQL 5.6 或更高版本
- 支持文件上传功能

### 标准插件安装（推荐）

#### 方法一：通过插件管理器安装

1. **上传插件包**
   ```bash
   # 解压插件包到插件目录
   unzip xinhu_resume_plugin_v1.0.zip -d xinhu/plugins/
   ```

2. **后台安装**
   - 登录信呼系统后台
   - 访问：系统管理 → 插件管理 → 安装插件
   - 选择"简历管理插件"
   - 点击"安装"按钮
   - 等待安装完成

3. **启用插件**
   - 安装完成后，在插件列表中启用插件
   - 刷新页面，查看"人事档案"菜单下的"简历管理"

#### 方法二：直接执行安装脚本

1. **上传插件包**
   ```bash
   # 上传到插件目录
   xinhu/plugins/resume_manager/
   ```

2. **执行安装**
   ```bash
   # 访问安装脚本
   http://your-domain.com/xinhu/plugins/resume_manager/install.php
   ```

3. **查看安装结果**
   - 安装脚本会返回JSON格式的安装结果
   - 检查安装日志确认所有步骤完成

### 传统方式安装（兼容）

如果系统不支持插件管理器，可以使用传统方式：

1. **手动复制文件**
   ```
   files/webmain/model/resumeModel.php → xinhu/webmain/model/
   files/webmain/main/resume/ → xinhu/webmain/main/resume/
   files/web/resume/ → xinhu/web/resume/
   ```

2. **执行SQL脚本**
   - 在数据库中执行 `sql/install.sql` 文件
   - 或通过"系统管理 → 系统升级"执行

3. **配置权限**
   - 在"系统管理 → 权限管理"中分配权限

## 📖 使用说明

### 外链简历填写

**访问地址：** `http://your-domain.com/xinhu/web/resume/`

求职者可以通过此链接：
- 填写个人基本信息
- 上传简历文件（支持PDF、DOC、DOCX）
- 填写工作经历、教育背景等详细信息
- 提交后等待HR审核

### 后台管理功能

#### 简历列表管理
- **路径：** 人事档案 → 简历管理 → 简历列表
- **功能：** 查看所有提交的简历，支持搜索和筛选
- **操作：** 查看详情、审核、删除、导出

#### 简历审核
- **路径：** 人事档案 → 简历管理 → 简历审核
- **功能：** 审核待处理的简历
- **状态：** 待审核 → 通过/拒绝 → 已入职

#### 简历统计
- **路径：** 人事档案 → 简历管理 → 简历统计
- **功能：** 查看简历提交和审核的统计数据

#### 转换员工档案
- 对于审核通过的简历，可以一键转换为员工档案
- 自动在"员工档案"中创建对应记录
- 简历状态更新为"已入职"

## ⚙️ 配置说明

### 文件上传配置

在 `xinhu/webmain/include/ResumeUploadHandler.php` 中可以修改：

```php
'max_file_size' => 5 * 1024 * 1024, // 文件大小限制（5MB）
'allowed_extensions' => ['pdf', 'doc', 'docx'], // 允许的文件类型
```

### 安全配置

在 `xinhu/webmain/include/ResumeSecurityManager.php` 中可以修改：

```php
'rate_limit' => [
    'submit_per_hour' => 10,    // 每小时最多提交10次
    'submit_per_day' => 50,     // 每天最多提交50次
]
```

### 菜单权限配置

在 `xinhu/webmain/model/modeModel.php` 中添加：

```php
$arr['resume'] = '菜单ID列表'; // 需要手动添加获取到的菜单ID
```

## 🔧 自定义开发

### 扩展字段

如需添加自定义字段，可以：

1. 修改数据表结构
2. 更新 `resumeModel.php` 中的字段处理
3. 修改前端表单和显示页面

### 集成其他模块

模块提供了标准的接口，可以与其他HR模块集成：

```php
// 获取简历数据
$resumeModel = m('resume');
$resume = $resumeModel->getResumeDetail($id);

// 转换为员工档案
$userId = $resumeModel->convertToEmployee($resumeId, $employeeData);
```

## 🛡️ 安全特性

### 访问控制
- IP频率限制
- 自动黑名单机制
- CSRF令牌验证

### 文件安全
- 文件类型验证
- 文件头检查
- 上传目录保护
- 可执行文件过滤

### 数据安全
- SQL注入防护
- XSS攻击防护
- 数据格式验证
- 敏感信息过滤

## 📊 数据表结构

### xinhu_resume（简历主表）
- 存储简历基本信息
- 包含审核状态和时间
- 记录提交IP和用户代理

### xinhu_resume_detail（简历详情表）
- 存储详细的简历信息
- 工作经历、教育背景等
- 与主表一对一关联

### xinhu_resume_log（操作日志表）
- 记录所有操作历史
- 审核记录和状态变更
- 便于追踪和审计

## 🔄 升级和维护

### 模块升级
1. 备份现有数据
2. 上传新版本文件
3. 执行升级SQL脚本
4. 测试功能正常

### 数据备份
```sql
-- 备份简历数据
SELECT * INTO OUTFILE '/backup/resume_backup.csv' 
FROM xinhu_resume;
```

### 日志清理
```php
// 清理30天前的日志
$securityManager = new ResumeSecurityManager($db);
$cleanedCount = $securityManager->cleanupLogs(30);
```

## 🐛 故障排除

### 常见问题

**Q: 外链页面无法访问**
A: 检查web服务器配置，确保xinhu/web/目录可以访问

**Q: 文件上传失败**
A: 检查PHP配置中的upload_max_filesize和post_max_size设置

**Q: 菜单不显示**
A: 检查权限配置，确保用户角色有访问权限

**Q: 数据库连接错误**
A: 检查数据库配置文件，确保连接信息正确

### 调试模式

在开发环境中，可以启用调试模式：

```php
// 在resume_submit.php开头添加
error_reporting(E_ALL);
ini_set('display_errors', 1);
```

## 📞 技术支持

### 开发团队
- **开发者：** 信呼开发团队
- **版本：** 1.0
- **发布日期：** 2025-06-21

### 更新日志

**v1.0 (2025-06-21)**
- 初始版本发布
- 完整的简历管理功能
- 安全和权限控制
- 文件上传管理
- 数据统计功能

## 📄 许可证

本模块遵循信呼OA系统的许可证协议。

---

**注意：** 安装前请务必备份数据库和文件，确保系统安全。如有问题，请及时联系技术支持。
